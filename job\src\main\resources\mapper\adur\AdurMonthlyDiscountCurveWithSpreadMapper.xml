<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.job.adur.mapper.AdurMonthlyDiscountCurveWithSpreadMapper">

    <resultMap type="com.xl.alm.job.adur.entity.AdurMonthlyDiscountCurveWithSpreadEntity" id="AdurMonthlyDiscountCurveWithSpreadEntityResult">
        <result property="id" column="id"/>
        <result property="accountPeriod" column="account_period"/>
        <result property="durationType" column="duration_type"/>
        <result property="basisPointType" column="basis_point_type"/>
        <result property="dateType" column="date_type"/>
        <result property="date" column="date"/>
        <result property="spreadType" column="spread_type"/>
        <result property="spread" column="spread"/>
        <result property="curveSubCategory" column="curve_sub_category"/>
        <result property="assetNumber" column="asset_number"/>
        <result property="accountName" column="account_name"/>
        <result property="assetName" column="asset_name"/>
        <result property="securityCode" column="security_code"/>
        <result property="curveId" column="curve_id"/>
        <result property="monthlyDiscountRateWithSpreadSet" column="monthly_discount_rate_with_spread_set"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDel" column="is_del"/>
    </resultMap>

    <sql id="selectAdurMonthlyDiscountCurveWithSpreadVo">
        select id, account_period, duration_type, basis_point_type, date_type, date, spread_type, spread,
               curve_sub_category, asset_number, account_name, asset_name, security_code, curve_id,
               monthly_discount_rate_with_spread_set,
               create_time, create_by, update_time, update_by, is_del
        from t_adur_monthly_discount_curve_with_spread
    </sql>

    <select id="selectByAccountPeriod" parameterType="String" resultMap="AdurMonthlyDiscountCurveWithSpreadEntityResult">
        <include refid="selectAdurMonthlyDiscountCurveWithSpreadVo"/>
        where account_period = #{accountPeriod}
        and is_del = 0
        order by asset_number, duration_type, basis_point_type
    </select>

    <select id="selectByCondition" resultMap="AdurMonthlyDiscountCurveWithSpreadEntityResult">
        <include refid="selectAdurMonthlyDiscountCurveWithSpreadVo"/>
        where account_period = #{accountPeriod}
        and asset_number = #{assetNumber}
        and duration_type = #{durationType}
        and basis_point_type = #{basisPointType}
        and is_del = 0
        limit 1
    </select>

    <delete id="deleteByAccountPeriod" parameterType="String">
        delete from t_adur_monthly_discount_curve_with_spread
        where account_period = #{accountPeriod}
    </delete>

    <insert id="batchInsertMonthlyDiscountCurveWithSpread" parameterType="java.util.List">
        insert into t_adur_monthly_discount_curve_with_spread(
            account_period, duration_type, basis_point_type, date_type, date, spread_type, spread,
            curve_sub_category, asset_number, account_name, asset_name, security_code, curve_id,
            monthly_discount_rate_with_spread_set,
            create_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.accountPeriod}, #{item.durationType}, #{item.basisPointType}, #{item.dateType}, #{item.date},
                #{item.spreadType}, #{item.spread}, #{item.curveSubCategory}, #{item.assetNumber}, #{item.accountName},
                #{item.assetName}, #{item.securityCode}, #{item.curveId},
                #{item.monthlyDiscountRateWithSpreadSet},
                #{item.createBy}
            )
        </foreach>
    </insert>

    <!-- 更新单个月度折现曲线含价差记录 -->
    <update id="updateById" parameterType="com.xl.alm.job.adur.entity.AdurMonthlyDiscountCurveWithSpreadEntity">
        update t_adur_monthly_discount_curve_with_spread
        set monthly_discount_rate_with_spread_set = #{monthlyDiscountRateWithSpreadSet},
            spread = #{spread},
            update_time = sysdate(),
            update_by = 'SYSTEM'
        where id = #{id}
    </update>

</mapper>
