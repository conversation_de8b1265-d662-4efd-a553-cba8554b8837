<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xl.alm.app.mapper.CostRevenueMatchPressureSummaryMapper">
    
    <resultMap type="com.xl.alm.app.entity.CostRevenueMatchPressureSummaryEntity" id="CostRevenueMatchPressureSummaryResult">
        <result property="id"    column="id"    />
        <result property="accountingPeriod"    column="accounting_period"    />
        <result property="accountName"    column="account_name"    />
        <result property="itemName"    column="item_name"    />
        <result property="changeValue"    column="change_value"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="isDel"    column="is_del"    />
    </resultMap>

    <sql id="selectCostRevenueMatchPressureSummaryVo">
        select id, accounting_period, account_name, item_name, change_value, create_by, create_time, update_by, update_time, is_del from t_pir_cost_revenue_match_pressure_summary
    </sql>

    <select id="selectCostRevenueMatchPressureSummaryList" parameterType="com.xl.alm.app.query.CostRevenueMatchPressureSummaryQuery" resultMap="CostRevenueMatchPressureSummaryResult">
        <include refid="selectCostRevenueMatchPressureSummaryVo"/>
        <where>  
            <if test="accountingPeriod != null  and accountingPeriod != ''"> and accounting_period = #{accountingPeriod}</if>
            <if test="accountName != null  and accountName != ''"> and account_name like concat('%', #{accountName}, '%')</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="changeValue != null "> and change_value = #{changeValue}</if>
            and is_del = 0
        </where>
    </select>
    
    <select id="selectCostRevenueMatchPressureSummaryById" parameterType="Long" resultMap="CostRevenueMatchPressureSummaryResult">
        <include refid="selectCostRevenueMatchPressureSummaryVo"/>
        where id = #{id} and is_del = 0
    </select>
        
    <insert id="insertCostRevenueMatchPressureSummary" parameterType="com.xl.alm.app.entity.CostRevenueMatchPressureSummaryEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_pir_cost_revenue_match_pressure_summary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="accountName != null and accountName != ''">account_name,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="changeValue != null">change_value,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="accountName != null and accountName != ''">#{accountName},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="changeValue != null">#{changeValue},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
        </trim>
    </insert>

    <update id="updateCostRevenueMatchPressureSummary" parameterType="com.xl.alm.app.entity.CostRevenueMatchPressureSummaryEntity">
        update t_pir_cost_revenue_match_pressure_summary
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="accountName != null and accountName != ''">account_name = #{accountName},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="changeValue != null">change_value = #{changeValue},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = now()
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCostRevenueMatchPressureSummaryById" parameterType="Long">
        update t_pir_cost_revenue_match_pressure_summary set is_del = 1 where id = #{id}
    </delete>

    <delete id="deleteCostRevenueMatchPressureSummaryByIds" parameterType="String">
        update t_pir_cost_revenue_match_pressure_summary set is_del = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCostRevenueMatchPressureSummaryByPeriod" parameterType="String">
        update t_pir_cost_revenue_match_pressure_summary set is_del = 1 where accounting_period = #{accountingPeriod}
    </delete>

    <insert id="batchInsertCostRevenueMatchPressureSummary" parameterType="java.util.List">
        insert into t_pir_cost_revenue_match_pressure_summary(
            accounting_period, account_name, item_name, change_value, create_by, update_by
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.accountingPeriod}, #{item.accountName}, #{item.itemName}, #{item.changeValue}, #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

</mapper>
