package com.xl.alm.app.entity;

import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 成本收益匹配压力一汇总表实体类
 *
 * <AUTHOR> Assistant
 */
@Data
public class CostRevenueMatchPressureSummaryEntity extends BaseEntity {
    
    /**
     * 主键
     */
    private Long id;
    
    /**
     * 账期，格式YYYYMM（如202406）
     */
    private String accountingPeriod;
    
    /**
     * 账户名称，引用字典ast_account_name_mapping
     */
    private String accountName;
    
    /**
     * 项目名称，建议新建字典pir_asset_category：01:固定收益类投资资产,02:权益类投资资产,03:投资性房地产
     */
    private String itemName;
    
    /**
     * 变动值，压力测试后的价值变动金额，单位：元
     */
    private BigDecimal changeValue;
    
    /**
     * 是否删除，0：否，1：是
     */
    private Integer isDel = 0;
}
