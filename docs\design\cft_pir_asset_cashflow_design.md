# 现金流测试与投资收益率预测系统功能设计文档

## 1. 业务架构

### 1.1 业务模块关系图

*描述CFT现金流测试模块和PIR投资收益率预测模块间的层级关系*

```mermaid
flowchart TD
    A[资产变现预测需求] --> B[CFT现金流测试模块]
    C[资产现金流预测] --> B[CFT现金流测试模块]
    D[本季度实际资产现金流] --> B[CFT现金流测试模块]
    E[成本收益匹配压力一] --> F[PIR投资收益率预测模块]
    B[CFT现金流测试模块] --> G[现金流测试分析]
    F[PIR投资收益率预测模块] --> H[投资收益率预测分析]
    G[现金流测试分析] --> I[资产负债管理决策支持]
    H[投资收益率预测分析] --> I[资产负债管理决策支持]
```

### 1.2 模块列表

*描述各模块的基础信息（编号、名称、英文名、英文缩写）*

| 模块编号   | 模块名称        | 模块英文名                           | 英文缩写 |
| ------ | ----------- | ------------------------------- | ---- |
| MD0001 | 现金流测试模块     | cash_flow_testing               | cft  |
| MD0002 | 投资收益率预测模块   | profit_investment_return        | pir  |

### 1.3 数据模型

#### 1.3.1 现金流测试模块（CFT）

*描述现金流测试模块下的表间关系及表属性信息*

#### 1.3.2 投资收益率预测模块（PIR）

*描述投资收益率预测模块下的表间关系及表属性信息*

##### 1.3.1.1 表间关系

*表间关系用mermaid图描述，主要用于梳理关系，使用英文描述*

```mermaid
erDiagram
    %% 引用外部表
    t_ast_asset_detail_overall ||--o{ t_cft_pledge_security_detail : "provides asset base data"
    t_ast_asset_detail_overall ||--o{ t_cft_asset_scale_liquidation_detail : "provides asset base data"
    t_adur_duration_asset_detail ||--o{ t_cft_asset_cashflow_forecast : "provides cashflow data"

    %% CFT现金流测试模块表间关系
    t_cft_pledge_security_detail ||--o{ t_cft_asset_scale_liquidation_detail : "provides pledge security data"
    t_cft_asset_scale_liquidation_detail ||--o{ t_cft_asset_scale_liquidation_summary : "aggregates to summary"
    t_cft_asset_cashflow_forecast ||--o{ t_cft_actual_asset_cashflow_dividend : "provides forecast data"
    t_cft_asset_cashflow_forecast ||--o{ t_cft_actual_asset_cashflow_fixed : "provides forecast data"
    t_cft_asset_cashflow_forecast ||--o{ t_cft_actual_asset_cashflow_almcf : "provides forecast data"

    %% ALMCF实际发生数表的复杂依赖关系
    t_base_cash_flow_statement ||--o{ t_cft_actual_asset_cashflow_almcf : "provides base cashflow data"
    t_base_cash_flow_item_mapping ||--o{ t_cft_actual_asset_cashflow_almcf : "provides mapping rules"
    t_cft_actual_asset_cashflow_dividend ||--o{ t_cft_actual_asset_cashflow_almcf : "provides dividend data"
    t_cft_actual_asset_cashflow_fixed ||--o{ t_cft_actual_asset_cashflow_almcf : "provides fixed income data"

    %% 引用已存在的表
    t_base_cash_flow_statement ||--o{ t_cft_asset_cashflow_forecast : "provides base cashflow data"
    t_base_cash_flow_item_mapping ||--o{ t_cft_asset_cashflow_forecast : "provides mapping rules"

    %% PIR投资收益率预测模块表间关系
    t_adur_duration_asset_detail ||--o{ t_pir_cost_revenue_match_pressure_detail : "provides duration asset data"
    t_ast_asset_detail_overall ||--o{ t_pir_cost_revenue_match_pressure_detail : "provides overall asset data"
    t_pir_cost_revenue_match_pressure_detail ||--o{ t_pir_cost_revenue_match_pressure_summary : "aggregates to summary"
```

**核心关系说明：**
- **t_ast_asset_detail_overall** 资产整体明细表（引用自asset_details_program_design.md中的TB0016表）是CFT模块的核心基础数据源
- **t_adur_duration_asset_detail** 久期资产明细表（引用自adur_program_design.md中的TB0003表）提供现金流值集数据
- **t_cft_pledge_security_detail** 质押券明细表通过手动导入获取，用于标识质押券信息
- **t_cft_asset_scale_liquidation_detail** 资产规模及变现金额明细表基于资产整体明细表和质押券明细表生成，支持基本情景和压力情景
- **t_cft_asset_scale_liquidation_summary** 资产规模及变现金额汇总表通过明细表聚合生成
- **t_cft_asset_cashflow_forecast** 资产现金流预测表通过解析久期资产明细表的现金流值集生成
- **t_cft_actual_asset_cashflow_almcf** ALMCF实际发生数表通过复杂计算逻辑生成，依赖现金流量表、现金流项目映射表、红利收入明细表、固收资产现金流表
- **t_base_cash_flow_statement** 现金流量表（引用自cft_libility_program_design.md中的TB0020表）为现金流预测提供历史数据基础
- **t_base_cash_flow_item_mapping** 现金流项目映射表（引用自cft_libility_program_design.md中的TB0021表）提供现金流项目分类映射关系
- **t_pir_cost_revenue_match_pressure_detail** 成本收益匹配压力明细表通过复杂计算逻辑生成，依赖久期资产明细表和整体资产明细表
- **t_pir_cost_revenue_match_pressure_summary** 成本收益匹配压力汇总表通过明细表按维度聚合生成

##### 1.3.1.2 表名字典

*列出现金流测试模块和投资收益率预测模块所有表信息*

**注意：** CFT模块特别依赖资产宽表模块中的资产整体明细表TB0016作为基础数据源

| 表编号    | 表中文名                | 表英文名                                      | 模块  | 备注                    |
| ------ | ------------------- | ----------------------------------------- | --- | --------------------- |
| TB0001 | 质押券明细表              | t_cft_pledge_security_detail              | cft | 资产变现预测需求基础数据，手动导入      |
| TB0002 | 资产规模及变现金额明细表        | t_cft_asset_scale_liquidation_detail      | cft | 资产变现预测明细统计表，基于资产整体明细表生成 |
| TB0003 | 资产规模及变现金额汇总表        | t_cft_asset_scale_liquidation_summary     | cft | 资产变现预测汇总统计表，由明细表聚合生成  |
| TB0004 | 资产现金流预测表            | t_cft_asset_cashflow_forecast             | cft | 资产现金流预测数据，基于久期资产明细表生成 |
| TB0005 | 现金流量表（已存在）          | t_base_cash_flow_statement                | cft | 引用cft_libility_program_design.md中的TB0020表 |
| TB0006 | 现金流项目映射表（已存在）       | t_base_cash_flow_item_mapping             | cft | 引用cft_libility_program_design.md中的TB0021表 |
| TB0007 | 红利收入明细表             | t_cft_actual_asset_cashflow_dividend      | cft | 本季度实际资产现金流-红利收入       |
| TB0008 | 固收资产现金流表            | t_cft_actual_asset_cashflow_fixed         | cft | 本季度实际资产现金流-固收资产       |
| TB0009 | ALMCF实际发生数表         | t_cft_actual_asset_cashflow_almcf         | cft | 本季度实际资产现金流-ALMCF数据，复杂计算生成 |
| TB0010 | 成本收益匹配压力一明细表        | t_pir_cost_revenue_match_pressure_detail  | pir | 成本收益匹配压力分析明细数据，复杂计算生成 |
| TB0011 | 成本收益匹配压力一汇总表        | t_pir_cost_revenue_match_pressure_summary | pir | 成本收益匹配压力分析汇总数据，由明细表聚合生成 |
| -      | 资产整体明细表（引用外部）       | t_ast_asset_detail_overall                | ast | 引用自asset_details_program_design.md中的TB0016表 |
| -      | 久期资产明细表（引用外部）       | t_adur_duration_asset_detail              | adur | 引用自adur_program_design.md中的TB0003表 |

##### 1.3.1.3 表集

*描述详细的表属性信息，id（默认为主键）、create_time、update_time、create_by、update_by、is_del等字段不需要描述，后续生成DDL时会自动补充*

**（1）TB0001 - 质押券明细表**

*存储质押券资产的详细信息，通过手动导入方式获取数据，用于资产变现预测需求分析*

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                    |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------------------- |
| **accounting_period**  | varchar | 6     | 否   | 是    | 无   | 账期，格式YYYYMM（如202406）                  |
| **account_name**       | varchar | 50    | 否   | 是    | 无   | 账户名称，引用字典ast_account_name_mapping |
| **asset_name**         | varchar | 100   | 否   | 是    | 无   | 资产名称，质押券资产名称                          |
| **security_code**      | varchar | 20    | 否   | 是    | 无   | 证券代码，质押券证券标识代码                        |
| face_value_total       | decimal | 30,10 | 是   | 否    | 0   | 券面总额，质押券券面总额，单位：元                     |

**数据来源：** 此表数据通过手动导入方式获取，不存在计算规则

**（2）TB0002 - 资产规模及变现金额明细表**

*存储资产规模及变现金额的明细数据，用于资产变现预测分析。该表基于资产整体明细表（引用自asset_details_program_design.md中的TB0016表）和质押券明细表生成，支持基本情景和压力情景两种模式*

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                    |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------------------- |
| **accounting_period**  | varchar | 6     | 否   | 是    | 无   | 账期，格式YYYYMM（如202406）                  |
| **scenario_name**      | varchar | 20    | 否   | 是    | 无   | 情景名称，引用字典cft_scenario_name                    |
| **asset_number**       | varchar | 20    | 否   | 否    | 无   | 资产编号，来源于整体资产明细表.资产编号                  |
| **account_name**       | varchar | 50    | 否   | 是    | 无   | 账户名称，来源于整体资产明细表.账户名称，引用字典ast_account_name_mapping |
| **asset_name**         | varchar | 100   | 否   | 否    | 无   | 资产名称，来源于整体资产明细表.资产名称                  |
| **security_code**      | varchar | 20    | 是   | 是    | 无   | 证券代码，来源于整体资产明细表.证券代码                  |
| holding_face_value     | decimal | 30,10 | 是   | 否    | 0   | 持仓面值，来源于整体资产明细表.持仓面值                  |
| book_balance           | decimal | 30,10 | 是   | 否    | 0   | 账面余额，来源于整体资产明细表.账面余额                  |
| adjusted_maturity_date | date    | -     | 是   | 否    | 无   | 调整到期日，来源于整体资产明细表.调整到期日               |
| calculable_cashflow_flag | varchar | 5     | 是   | 否    | 0   | 可计算现金流固收资产标识，来源于整体资产明细表.可计算现金流固收资产标识 |
| asset_liquidity_category | varchar | 20    | 是   | 否    | 无   | 资产流动性分类，来源于整体资产明细表.资产流动性分类，01:高流动性资产,02:中低流动性资产,03:现金及流动性管理工具 |
| liquidation_coefficient | decimal | 10,6  | 是   | 否    | 0   | 变现系数，来源于整体资产明细表.变现系数                  |
| pledge_flag            | varchar | 5     | 是   | 否    | 0   | 质押标识，通过质押券明细表匹配确定                     |
| pledge_ratio           | decimal | 10,6  | 是   | 否    | 0   | 质押比例，质押券面额/持仓面值                       |
| evaluation_time_ms1    | decimal | 30,10 | 是   | 否    | 0   | 评估时点MS1（规模-全量券），等于账面余额               |
| future_q1_ms1          | decimal | 30,10 | 是   | 否    | 0   | 未来第一季度末MS1（规模-全量券）                   |
| future_q2_ms1          | decimal | 30,10 | 是   | 否    | 0   | 未来第二季度末MS1（规模-全量券）                   |
| future_q3_ms1          | decimal | 30,10 | 是   | 否    | 0   | 未来第三季度末MS1（规模-全量券）                   |
| future_q4_ms1          | decimal | 30,10 | 是   | 否    | 0   | 未来第四季度末MS1（规模-全量券）                   |
| future_y2_ms1          | decimal | 30,10 | 是   | 否    | 0   | 未来第二年末MS1（规模-全量券）                     |
| future_y3_ms1          | decimal | 30,10 | 是   | 否    | 0   | 未来第三年末MS1（规模-全量券）                     |
| evaluation_time_ms2    | decimal | 30,10 | 是   | 否    | 0   | 评估时点MS2（规模-质押券），等于账面余额*质押比例          |
| future_q1_ms2          | decimal | 30,10 | 是   | 否    | 0   | 未来第一季度末MS2（规模-质押券）                   |
| future_q2_ms2          | decimal | 30,10 | 是   | 否    | 0   | 未来第二季度末MS2（规模-质押券）                   |
| future_q3_ms2          | decimal | 30,10 | 是   | 否    | 0   | 未来第三季度末MS2（规模-质押券）                   |
| future_q4_ms2          | decimal | 30,10 | 是   | 否    | 0   | 未来第四季度末MS2（规模-质押券）                   |
| future_y2_ms2          | decimal | 30,10 | 是   | 否    | 0   | 未来第二年末MS2（规模-质押券）                     |
| future_y3_ms2          | decimal | 30,10 | 是   | 否    | 0   | 未来第三年末MS2（规模-质押券）                     |
| future_q1_ma           | decimal | 30,10 | 是   | 否    | 0   | 未来第一季度末MA（变现金额）                       |
| future_q2_ma           | decimal | 30,10 | 是   | 否    | 0   | 未来第二季度末MA（变现金额）                       |
| future_q3_ma           | decimal | 30,10 | 是   | 否    | 0   | 未来第三季度末MA（变现金额）                       |
| future_q4_ma           | decimal | 30,10 | 是   | 否    | 0   | 未来第四季度末MA（变现金额）                       |
| future_y2_ma           | decimal | 30,10 | 是   | 否    | 0   | 未来第二年末MA（变现金额）                         |
| future_y3_ma           | decimal | 30,10 | 是   | 否    | 0   | 未来第三年末MA（变现金额）                         |

**（3）TB0003 - 资产规模及变现金额汇总表**

*存储资产规模及变现金额的汇总数据，通过明细表按情景名称、账户名称、资产流动性分类进行汇总生成*

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                    |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------------------- |
| **accounting_period**  | varchar | 6     | 否   | 是    | 无   | 账期，格式YYYYMM（如202406）                  |
| **scenario_name**      | varchar | 20    | 否   | 是    | 无   | 情景名称，引用字典cft_scenario_name                    |
| **account_name**       | varchar | 50    | 否   | 是    | 无   | 账户名称，引用字典ast_account_name_mapping |
| **asset_liquidity_category** | varchar | 50    | 否   | 是    | 无   | 资产流动性分类，01:高流动性资产,02:中低流动性资产,03:现金及流动性管理工具 |
| evaluation_time_ms1    | decimal | 30,10 | 是   | 否    | 0   | 评估时点MS1（规模-全量券）                       |
| future_q1_ms1          | decimal | 30,10 | 是   | 否    | 0   | 未来第一季度末MS1（规模-全量券）                   |
| future_q2_ms1          | decimal | 30,10 | 是   | 否    | 0   | 未来第二季度末MS1（规模-全量券）                   |
| future_q3_ms1          | decimal | 30,10 | 是   | 否    | 0   | 未来第三季度末MS1（规模-全量券）                   |
| future_q4_ms1          | decimal | 30,10 | 是   | 否    | 0   | 未来第四季度末MS1（规模-全量券）                   |
| future_y2_ms1          | decimal | 30,10 | 是   | 否    | 0   | 未来第二年末MS1（规模-全量券）                     |
| future_y3_ms1          | decimal | 30,10 | 是   | 否    | 0   | 未来第三年末MS1（规模-全量券）                     |
| evaluation_time_ms2    | decimal | 30,10 | 是   | 否    | 0   | 评估时点MS2（规模-质押券）                       |
| future_q1_ms2          | decimal | 30,10 | 是   | 否    | 0   | 未来第一季度末MS2（规模-质押券）                   |
| future_q2_ms2          | decimal | 30,10 | 是   | 否    | 0   | 未来第二季度末MS2（规模-质押券）                   |
| future_q3_ms2          | decimal | 30,10 | 是   | 否    | 0   | 未来第三季度末MS2（规模-质押券）                   |
| future_q4_ms2          | decimal | 30,10 | 是   | 否    | 0   | 未来第四季度末MS2（规模-质押券）                   |
| future_y2_ms2          | decimal | 30,10 | 是   | 否    | 0   | 未来第二年末MS2（规模-质押券）                     |
| future_y3_ms2          | decimal | 30,10 | 是   | 否    | 0   | 未来第三年末MS2（规模-质押券）                     |
| future_q1_ma           | decimal | 30,10 | 是   | 否    | 0   | 未来第一季度末MA（变现金额）                       |
| future_q2_ma           | decimal | 30,10 | 是   | 否    | 0   | 未来第二季度末MA（变现金额）                       |
| future_q3_ma           | decimal | 30,10 | 是   | 否    | 0   | 未来第三季度末MA（变现金额）                       |
| future_q4_ma           | decimal | 30,10 | 是   | 否    | 0   | 未来第四季度末MA（变现金额）                       |
| future_y2_ma           | decimal | 30,10 | 是   | 否    | 0   | 未来第二年末MA（变现金额）                         |
| future_y3_ma           | decimal | 30,10 | 是   | 否    | 0   | 未来第三年末MA（变现金额）                         |

**（4）TB0004 - 资产现金流预测表**

*存储资产现金流预测数据，通过解析久期资产明细表（引用自adur_program_design.md中的TB0003表）的现金流值集生成，用于现金流测试分析*

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                    |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------------------- |
| **accounting_period**  | varchar | 6     | 否   | 是    | 无   | 账期，格式YYYYMM（如202406）                  |
| **scenario_name**      | varchar | 20    | 否   | 是    | 无   | 情景名称，引用字典cft_scenario_name                    |
| **account_name**       | varchar | 50    | 否   | 是    | 无   | 账户名称，引用字典ast_account_name_mapping |
| **item_name**          | varchar | 50    | 否   | 是    | 无   | 现金流项目类型，建议新建字典cft_cashflow_item_type：01:利息收入,02:到期资产 |
| future_q1              | decimal | 30,10 | 是   | 否    | 0   | 未来第一季度现金流金额，单位：元                       |
| future_q2              | decimal | 30,10 | 是   | 否    | 0   | 未来第二季度现金流金额，单位：元                       |
| future_q3              | decimal | 30,10 | 是   | 否    | 0   | 未来第三季度现金流金额，单位：元                       |
| future_q4              | decimal | 30,10 | 是   | 否    | 0   | 未来第四季度现金流金额，单位：元                       |
| future_y2_remaining    | decimal | 30,10 | 是   | 否    | 0   | 未来第二年剩余季度现金流金额，单位：元                    |
| future_y3              | decimal | 30,10 | 是   | 否    | 0   | 未来第三年现金流金额，单位：元                        |

**数据来源：** 此表数据通过解析adur_program_design.md中TB0003久期资产明细表的principal_cashflow_set（本金现金流值集）和interest_cashflow_set（利息现金流值集）字段生成

**（5）TB0005 - 现金流量表（已存在）**

*此表引用自cft_libility_program_design.md中的TB0020表，存储历史现金流量数据，为现金流预测提供基础数据*

*完整的表结构定义请参考cft_libility_program_design.md中的t_base_cash_flow_statement表定义*

**（6）TB0006 - 现金流项目映射表（已存在）**

*此表引用自cft_libility_program_design.md中的TB0021表，存储现金流项目分类映射关系*

*完整的表结构定义请参考cft_libility_program_design.md中的t_base_cash_flow_item_mapping表定义*

**（7）TB0007 - 红利收入明细表**

*存储本季度实际资产现金流中的红利收入明细数据，通过手动导入方式获取数据*

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                    |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------------------- |
| **accounting_period**  | varchar | 6     | 否   | 是    | 无   | 账期，格式YYYYMM（如202406）                  |
| **account_name**       | varchar | 50    | 否   | 是    | 无   | 账户名称，引用字典ast_account_name_mapping |
| **asset_sub_sub_category** | varchar | 50    | 否   | 是    | 无   | 资产小小类分类，引用字典ast_asset_sub_sub_category |
| **account_set_name**   | varchar | 100   | 否   | 是    | 无   | 账套名称，具体账套标识                           |
| **security_code**      | varchar | 20    | 否   | 是    | 无   | 证券代码，证券唯一标识                           |
| security_name          | varchar | 100   | 否   | 否    | 无   | 证券名称，证券具体名称                           |
| voucher_date           | date    | -     | 否   | 否    | 无   | 凭证日期，红利收入凭证日期                         |
| received_amount        | decimal | 30,10 | 否   | 否    | 0   | 到账金额，红利收入到账金额，单位：元                    |

**数据来源：** 此表数据通过手动导入方式获取，不存在计算规则

**（8）TB0008 - 固收资产现金流表**

*存储本季度实际资产现金流中的固收资产现金流数据，通过手动导入方式获取数据*

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                    |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------------------- |
| **accounting_period**  | varchar | 6     | 否   | 是    | 无   | 账期，格式YYYYMM（如202406）                  |
| **account_name**       | varchar | 50    | 否   | 是    | 无   | 账户名称，引用字典ast_account_name_mapping |
| **cash_flow_date**     | date    | -     | 否   | 是    | 无   | 现金流日期，现金流发生日期                         |
| **security_code**      | varchar | 20    | 否   | 是    | 无   | 证券代码，证券唯一标识                           |
| security_name          | varchar | 100   | 否   | 否    | 无   | 证券简称，证券名称                             |
| cashflow_interest      | decimal | 30,10 | 是   | 否    | 0   | 现金流付息，付息现金流金额，单位：元                    |
| cashflow_principal     | decimal | 30,10 | 是   | 否    | 0   | 现金流本金，本金现金流金额，单位：元                    |

**数据来源：** 此表数据通过手动导入方式获取，不存在计算规则

**（9）TB0009 - ALMCF实际发生数表**

*存储本季度实际资产现金流中的ALMCF（资产负债管理现金流）实际发生数据，通过复杂计算逻辑生成，支持本年累计和本季度累计两种统计类型*

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                    |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------------------- |
| **accounting_period**  | varchar | 6     | 否   | 是    | 无   | 账期，格式YYYYMM（如202406）                  |
| **item_name**          | varchar | 100   | 否   | 是    | 无   | 现金流项目名称                               |
| **statistics_type**    | varchar | 20    | 否   | 是    | 无   | 统计类型，建议新建字典cft_statistics_type：01:本年累计,02:本季度累计 |
| company_overall        | decimal | 30,10 | 是   | 否    | 0   | 公司整体现金流金额，单位：元                        |
| general_account        | decimal | 30,10 | 是   | 否    | 0   | 普通账户现金流金额，单位：元                        |
| traditional_account    | decimal | 30,10 | 是   | 否    | 0   | 传统账户现金流金额，单位：元                        |
| participating_account  | decimal | 30,10 | 是   | 否    | 0   | 分红账户现金流金额，单位：元                        |
| universal_account      | decimal | 30,10 | 是   | 否    | 0   | 万能账户现金流金额，单位：元                        |
| unit_linked_account    | decimal | 30,10 | 是   | 否    | 0   | 投连账户现金流金额，单位：元                        |

**数据来源：** 此表数据通过复杂计算逻辑生成，参考cft_libility_program_design.md中UC0022和UC0023的计算方法

**（10）TB0010 - 成本收益匹配压力一明细表**

*存储成本收益匹配压力分析的明细数据，通过复杂计算逻辑生成，基于久期资产明细表和整体资产明细表数据*

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                    |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------------------- |
| **accounting_period**  | varchar | 6     | 否   | 是    | 无   | 账期，格式YYYYMM（如202406）                  |
| **account_name**       | varchar | 50    | 否   | 是    | 无   | 账户名称，引用字典ast_account_name_mapping |
| book_value_1_before_stress | decimal | 30,10 | 是   | 否    | 0   | 账面价值1压力前，利差久期资产压力前账面价值，单位：元         |
| book_value_1_after_stress | decimal | 30,10 | 是   | 否    | 0   | 账面价值1压力后，利差久期资产压力后账面价值，单位：元         |
| book_value_1_change    | decimal | 30,10 | 是   | 否    | 0   | 账面价值1变动值，利差久期资产价值变动金额，单位：元          |
| book_value_2_before_stress | decimal | 30,10 | 是   | 否    | 0   | 账面价值2压力前，债券型基金压力前账面价值，单位：元          |
| book_value_2_after_stress | decimal | 30,10 | 是   | 否    | 0   | 账面价值2压力后，债券型基金压力后账面价值，单位：元          |
| book_value_2_change    | decimal | 30,10 | 是   | 否    | 0   | 账面价值2变动值，债券型基金价值变动金额，单位：元           |
| book_value_3_before_stress | decimal | 30,10 | 是   | 否    | 0   | 账面价值3压力前，固收资管产品压力前账面价值，单位：元         |
| book_value_3_after_stress | decimal | 30,10 | 是   | 否    | 0   | 账面价值3压力后，固收资管产品压力后账面价值，单位：元         |
| book_value_3_change    | decimal | 30,10 | 是   | 否    | 0   | 账面价值3变动值，固收资管产品价值变动金额，单位：元          |
| book_value_4_before_stress | decimal | 30,10 | 是   | 否    | 0   | 账面价值4压力前，五级分类资产压力前账面价值，单位：元         |
| book_value_4_after_stress | decimal | 30,10 | 是   | 否    | 0   | 账面价值4压力后，五级分类资产压力后账面价值，单位：元         |
| book_value_4_change    | decimal | 30,10 | 是   | 否    | 0   | 账面价值4变动值，五级分类资产价值变动金额，单位：元          |
| book_value_5_before_stress | decimal | 30,10 | 是   | 否    | 0   | 账面价值5压力前，股票基金压力前账面价值，单位：元           |
| book_value_5_after_stress | decimal | 30,10 | 是   | 否    | 0   | 账面价值5压力后，股票基金压力后账面价值，单位：元           |
| book_value_5_change    | decimal | 30,10 | 是   | 否    | 0   | 账面价值5变动值，股票基金价值变动金额，单位：元            |
| book_value_6_before_stress | decimal | 30,10 | 是   | 否    | 0   | 账面价值6压力前，其他权益类压力前账面价值，单位：元          |
| book_value_6_after_stress | decimal | 30,10 | 是   | 否    | 0   | 账面价值6压力后，其他权益类压力后账面价值，单位：元          |
| book_value_6_change    | decimal | 30,10 | 是   | 否    | 0   | 账面价值6变动值，其他权益类价值变动金额，单位：元           |
| book_value_7_before_stress | decimal | 30,10 | 是   | 否    | 0   | 账面价值7压力前，投资性房地产压力前账面价值，单位：元         |
| book_value_7_after_stress | decimal | 30,10 | 是   | 否    | 0   | 账面价值7压力后，投资性房地产压力后账面价值，单位：元         |
| book_value_7_change    | decimal | 30,10 | 是   | 否    | 0   | 账面价值7变动值，投资性房地产价值变动金额，单位：元          |

**数据来源：** 此表数据通过复杂计算逻辑生成，基于久期资产明细表和整体资产明细表数据

**特殊UI需求：** 页面需要提供压力测试说明的悬浮提示功能，当鼠标悬停在相关字段时显示7条压力测试规则说明

**（11）TB0011 - 成本收益匹配压力一汇总表**

*存储成本收益匹配压力分析的汇总数据，通过明细表按账户名称和项目类型聚合生成*

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                    |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------------------- |
| **accounting_period**  | varchar | 6     | 否   | 是    | 无   | 账期，格式YYYYMM（如202406）                  |
| **account_name**       | varchar | 50    | 否   | 是    | 无   | 账户名称，引用字典ast_account_name_mapping |
| **item_name**          | varchar | 50    | 否   | 是    | 无   | 项目名称，建议新建字典pir_asset_category：01:固定收益类投资资产,02:权益类投资资产,03:投资性房地产 |
| change_value           | decimal | 30,10 | 是   | 否    | 0   | 变动值，压力测试后的价值变动金额，单位：元                |

**数据来源：** 此表数据通过明细表聚合生成

**特殊UI需求：** 页面需要提供压力测试说明的悬浮提示功能，当鼠标悬停在相关字段时显示7条压力测试规则说明

## 2. 业务规则说明

### 2.1 CFT现金流测试模块业务规则

#### 2.1.1 数据生成流程

**第一步：基础情景数据生成**
1. 从资产整体明细表（t_ast_asset_detail_overall）获取基础资产数据
2. 手动导入质押券明细表数据
3. 基于资产整体明细表和质押券明细表生成资产规模及变现金额明细表的基本情景数据
4. **重要说明**：资产整体明细表本身不区分基础情景和压力情景，从该表获取的数据默认标记为基本情景

**第二步：压力情景数据生成**
1. 复制基本情景数据作为压力情景的基础
2. 将情景名称字段更新为"压力情景"
3. 调整压力情景下的变现系数（通常比基本情景更保守）
4. 重新计算压力情景下的各项指标

#### 2.1.2 质押券明细表（TB0001）计算规则

1. **基础数据验证**
   - 账期格式必须为YYYYMM格式
   - 同一账期、账户、证券代码组合唯一

#### 2.1.3 资产规模及变现金额明细表（TB0002）计算规则

1. **质押标识计算**
   ```
   如果在质押券明细表(TB0001)中能找到匹配记录（按账户名称、证券代码匹配）：
       质押标识 = 1
   否则：
       质押标识 = 0
   ```

2. **质押比例计算**
   ```
   如果质押标识 = 0：
       质押比例 = 0
   如果质押标识 = 1：
       质押比例 = 质押券明细表.券面总额 / 资产规模及变现金额明细表.持仓面值
   ```

3. **评估时点规模计算**
   ```
   评估时点MS1（全量券）= 账面余额
   评估时点MS2（质押券）= 账面余额 * 质押比例
   ```

4. **未来各期规模计算（MS1-全量券）**
   ```
   对于未来第N季度末MS1：
   如果 调整到期日 <= 未来第N季度末对应日期 AND 可计算现金流固收资产标识 = 1：
       未来第N季度末MS1 = 0
   如果 调整到期日 > 未来第N季度末对应日期 AND 可计算现金流固收资产标识 = 1：
       未来第N季度末MS1 = 账面余额
   如果 可计算现金流固收资产标识 = 0：
       未来第N季度末MS1 = 账面余额
   ```

5. **未来各期规模计算（MS2-质押券）**
   ```
   对于未来第N季度末MS2：
   如果 调整到期日 <= 未来第N季度末对应日期 AND 可计算现金流固收资产标识 = 1：
       未来第N季度末MS2 = 0
   如果 调整到期日 > 未来第N季度末对应日期 AND 可计算现金流固收资产标识 = 1：
       未来第N季度末MS2 = 账面余额 * 质押比例
   如果 可计算现金流固收资产标识 = 0：
       未来第N季度末MS2 = 账面余额 * 质押比例
   ```

6. **变现金额计算（MA）**
   ```
   未来第N季度末MA = 未来第N季度末MS1 * (1 - 质押比例) * 变现系数
   ```

#### 2.1.4 资产规模及变现金额汇总表（TB0003）计算规则

1. **汇总维度**
   - 按情景名称、账户名称、资产流动性分类进行汇总

2. **账户名称特殊处理**
   ```
   如果账户名称 = "普通账户"：
       汇总时包含：传统账户 + 分红账户 + 万能账户
   ```

3. **汇总计算**
   ```
   各时点MS1 = SUM(明细表对应字段)
   各时点MS2 = SUM(明细表对应字段)
   各时点MA = SUM(明细表对应字段)
   ```

#### 2.1.5 资产现金流预测表（TB0004）计算规则

1. **数据源获取**
   ```
   从adur_program_design.md中TB0003久期资产明细表获取：
   - principal_cashflow_set（本金现金流值集）
   - interest_cashflow_set（利息现金流值集）
   ```

2. **现金流值集格式解析**
   ```json
   现金流值集JSON格式：
   {
     "0": {"date": "2025-07-31", "value": "-30000000.0000000000"},
     "1": {"date": "2025-08-31", "value": "0"},
     "5": {"date": "2025-12-31", "value": "32925000.00000000000000000000"}
   }
   ```

3. **季度划分规则**
   ```
   根据JSON中的date字段确定季度归属：
   - 未来第一季度：账期后1-3个月
   - 未来第二季度：账期后4-6个月
   - 未来第三季度：账期后7-9个月
   - 未来第四季度：账期后10-12个月
   - 未来第二年剩余季度：账期后13-24个月
   - 未来第三年：账期后25-36个月
   ```

4. **项目分类计算**
   ```
   利息收入项目：
   - 解析interest_cashflow_set字段
   - 按季度汇总利息现金流value值

   到期资产项目：
   - 解析principal_cashflow_set字段
   - 按季度汇总本金现金流value值
   ```

5. **情景处理规则**
   ```
   基本情景和压力情景：
   - 使用相同的现金流值集数据
   - 仅情景名称字段不同
   - 计算逻辑完全一致
   ```

#### 2.1.6 ALMCF实际发生数表（TB0009）计算规则

**说明：** 此表合并了本年累计和本季度累计两种统计类型，参考cft_libility_program_design.md中UC0022和UC0023的计算方法

**数据源表：**
- TB0005: 现金流量表（已存在）- t_base_cash_flow_statement
- TB0006: 现金流项目映射表（已存在）- t_base_cash_flow_item_mapping
- TB0007: 红利收入明细表 - t_cft_actual_asset_cashflow_dividend
- TB0008: 固收资产现金流表 - t_cft_actual_asset_cashflow_fixed

##### ******* 本年累计计算方法

**1. 公司整体账户计算规则：**

```
5. 如果项目="2.资产现金流"、"其中：股东增资"、"次级债"、"卖出回购金融资产"、"筹资现金流入"、"支付借款利息和债券利息"、"其中：回购金融资产"、"减：筹资现金流出"、"3.筹资现金流"：
   （1）通过现金流项目映射表转换为现金流量表中的项目
   （2）查询列：现金流量表.公司整体账户   匹配字段：现金流量表.项目

6. 如果项目="红利收入"：
   - 等于SUM(红利收入明细表.到账金额)
   - 匹配条件：红利收入明细表.凭证日期 <= 账期

7. 如果项目="到期资产"：
   - 等于SUM(固收资产现金流表.现金流本金)
   - 匹配条件：固收资产现金流表.日期 <= 账期

8. 如果项目="利息收入"：
   - 等于红利收入 - 现金流量表.公司整体
   （1）通过现金流项目映射表转换为现金流量表中的项目
   （2）查询列：现金流量表.公司整体   匹配字段：现金流量表.项目

9. 如果项目="出售资产"：
   - 等于现金流量表.公司整体 - 到期资产
   （1）通过现金流项目映射表转换为现金流量表中的项目
   （2）查询列：现金流量表.公司整体   匹配字段：现金流量表.项目

10. 如果项目="其他资产现金流"：
    - 等于"2.资产现金流" - 红利收入 - 到期资产 - 利息收入 - 出售资产
    （1）通过现金流项目映射表转换为现金流量表中的项目
    （2）查询列：现金流量表.公司整体   匹配字段：现金流量表.项目
```

**2. 普通账户计算规则：**
```
普通账户 = 公司整体 - 投连账户
```

**3. 传统账户计算规则：**
```
传统账户 = 公司整体 - 分红账户 - 万能账户 - 投连账户
```

**4. 分红账户计算规则：**
```
5. 如果项目="2.资产现金流"、"其中：股东增资"、"次级债"、"卖出回购金融资产"、"筹资现金流入"、"支付借款利息和债券利息"、"其中：回购金融资产"、"减：筹资现金流出"、"3.筹资现金流"：
   （1）通过现金流项目映射表转换为现金流量表中的项目
   （2）查询列：现金流量表.分红账户   匹配字段：现金流量表.项目

6. 如果项目="红利收入"：
   - 等于SUM(红利收入明细表.到账金额)
   - 匹配条件：红利收入明细表.凭证日期 <= 账期 AND 红利收入明细表.账户名称 = 分红账户

7. 如果项目="到期资产"：
   - 等于SUM(固收资产现金流表.现金流本金)
   - 匹配条件：固收资产现金流表.日期 <= 账期 AND 固收资产现金流表.账户名称 = 分红账户

8. 如果项目="利息收入"：
   - 等于红利收入 - 现金流量表.分红账户
   （1）通过现金流项目映射表转换为现金流量表中的项目
   （2）查询列：现金流量表.分红账户   匹配字段：现金流量表.项目

9. 如果项目="出售资产"：
   - 等于现金流量表.分红账户 - 到期资产
   （1）通过现金流项目映射表转换为现金流量表中的项目
   （2）查询列：现金流量表.分红账户   匹配字段：现金流量表.项目

10. 如果项目="其他资产现金流"：
    - 等于"2.资产现金流" - 红利收入 - 到期资产 - 利息收入 - 出售资产
    （1）通过现金流项目映射表转换为现金流量表中的项目
    （2）查询列：现金流量表.分红账户   匹配字段：现金流量表.项目
```

**5. 万能账户计算规则：**
```
按照账户名称汇总：
5. 如果项目="2.资产现金流"、"其中：股东增资"、"次级债"、"卖出回购金融资产"、"筹资现金流入"、"支付借款利息和债券利息"、"其中：回购金融资产"、"减：筹资现金流出"、"3.筹资现金流"：
   （1）通过现金流项目映射表转换为现金流量表中的项目
   （2）查询列：现金流量表.万能账户   匹配字段：现金流量表.项目

6. 如果项目="红利收入"：
   - 等于SUM(红利收入明细表.到账金额)
   - 匹配条件：红利收入明细表.凭证日期 <= 账期 AND 红利收入明细表.账户名称 = 万能账户

7. 如果项目="到期资产"：
   - 等于SUM(固收资产现金流表.现金流本金)
   - 匹配条件：固收资产现金流表.日期 <= 账期 AND 固收资产现金流表.账户名称 = 万能账户

8. 如果项目="利息收入"：
   - 等于红利收入 - 现金流量表.万能账户
   （1）通过现金流项目映射表转换为现金流量表中的项目
   （2）查询列：现金流量表.万能账户   匹配字段：现金流量表.项目

9. 如果项目="出售资产"：
   - 等于现金流量表.万能账户 - 到期资产
   （1）通过现金流项目映射表转换为现金流量表中的项目
   （2）查询列：现金流量表.万能账户   匹配字段：现金流量表.项目

10. 如果项目="其他资产现金流"：
    - 等于"2.资产现金流" - 红利收入 - 到期资产 - 利息收入 - 出售资产
    （1）通过现金流项目映射表转换为现金流量表中的项目
    （2）查询列：现金流量表.万能账户   匹配字段：现金流量表.项目
```

**6. 投连账户计算规则：**
```
5. 如果项目="2.资产现金流"、"其中：股东增资"、"次级债"、"卖出回购金融资产"、"筹资现金流入"、"支付借款利息和债券利息"、"其中：回购金融资产"、"减：筹资现金流出"、"3.筹资现金流"：
   （1）通过现金流项目映射表转换为现金流量表中的项目
   （2）查询列：现金流量表.投连账户   匹配字段：现金流量表.项目

6. 如果项目="红利收入"：
   - 等于SUM(红利收入明细表.到账金额)
   - 匹配条件：红利收入明细表.凭证日期 <= 账期 AND 红利收入明细表.账户名称 = 投连账户

7. 如果项目="到期资产"：
   - 等于SUM(固收资产现金流表.现金流本金)
   - 匹配条件：固收资产现金流表.日期 <= 账期 AND 固收资产现金流表.账户名称 = 投连账户

8. 如果项目="利息收入"：
   - 等于红利收入 - 现金流量表.投连账户
   （1）通过现金流项目映射表转换为现金流量表中的项目
   （2）查询列：现金流量表.投连账户   匹配字段：现金流量表.项目

9. 如果项目="出售资产"：
   - 等于现金流量表.投连账户 - 到期资产
   （1）通过现金流项目映射表转换为现金流量表中的项目
   （2）查询列：现金流量表.投连账户   匹配字段：现金流量表.项目

10. 如果项目="其他资产现金流"：
    - 等于"2.资产现金流" - 红利收入 - 到期资产 - 利息收入 - 出售资产
    （1）通过现金流项目映射表转换为现金流量表中的项目
    （2）查询列：现金流量表.投连账户   匹配字段：现金流量表.项目
```

##### 2.1.6.2 本季度累计计算方法

**1. 公司整体账户本季度累计计算规则：**
```
1. 非1月/2月/3月：
   取自ALMCF实际发生数表本年累计表.公司整体，本账期数 - 上一季度月对应的账期数
2. 1月/2月/3月：
   直接取ALMCF实际发生数表本年累计表.公司整体
```

**2. 普通账户本季度累计计算规则：**
```
1. 非1月/2月/3月：
   取自ALMCF实际发生数表本年累计表.普通账户，本账期数 - 上一季度月对应的账期数
2. 1月/2月/3月：
   直接取ALMCF实际发生数表本年累计表.普通账户
```

**3. 传统账户本季度累计计算规则：**
```
1. 非1月/2月/3月：
   取自ALMCF实际发生数表本年累计表.传统账户，本账期数 - 上一季度月对应的账期数
2. 1月/2月/3月：
   直接取ALMCF实际发生数表本年累计表.传统账户
```

**4. 分红账户本季度累计计算规则：**
```
1. 非1月/2月/3月：
   取自ALMCF实际发生数表本年累计表.分红账户，本账期数 - 上一季度月对应的账期数
2. 1月/2月/3月：
   直接取ALMCF实际发生数表本年累计表.分红账户
```

**5. 万能账户本季度累计计算规则：**
```
1. 非1月/2月/3月：
   取自ALMCF实际发生数表本年累计表.万能账户，本账期数 - 上一季度月对应的账期数
2. 1月/2月/3月：
   直接取ALMCF实际发生数表本年累计表.万能账户
```

**6. 投连账户本季度累计计算规则：**
```
1. 非1月/2月/3月：
   取自ALMCF实际发生数表本年累计表.投连账户，本账期数 - 上一季度月对应的账期数
2. 1月/2月/3月：
   直接取ALMCF实际发生数表本年累计表.投连账户
```

#### 2.1.7 情景差异说明

**基本情景与压力情景的主要差异：**
1. **变现系数不同**：压力情景下变现系数通常更低（更保守）
2. **计算逻辑相同**：两种情景下的计算公式完全一致，仅参数不同
3. **数据来源相同**：都基于相同的资产整体明细表和质押券明细表

### 2.2 PIR投资收益率预测模块业务规则

#### 2.2.1 成本收益匹配压力一明细表（TB0010）计算规则

**数据来源：**
- 久期资产明细表（adur_program_design.md中的TB0003表）
- 整体资产明细表（asset_details_program_design.md中的TB0016表）
- 风险10日VaR值表（acm_program_design.md中的TB0005表）

**各账面价值计算规则：**

1. **账面价值1（利差久期资产）**
   ```
   压力前 = SUM(久期资产明细表.账面价值)
   匹配条件：久期资产明细表.利差久期资产统计标识 = 1 AND 账户名称匹配

   压力后 = SUM(久期资产明细表.账面价值σ=77%)
   匹配条件：同上

   变动值 = 压力前 - 压力后
   ```

2. **账面价值2（债券型基金）**
   ```
   压力前 = SUM(整体资产明细表.账面价值)
   匹配条件：整体资产明细表.资产小小类 = 债券型基金 AND 账户名称匹配 AND 境内外标识 = 境外资产

   压力后 = 压力前 - 变动值
   变动值 = 待确定计算规则
   ```

3. **账面价值3（固收资管产品）**
   ```
   压力前 = SUM(整体资产明细表.账面价值)
   匹配条件：[整体资产明细表.资产小小类 = 固定收益类保险资产管理产品 OR
            (整体资产明细表.资产小小类 = 债券型基金 AND 整体资产明细表.境内外 = 境外资产)]
            AND 账户名称匹配

   变动值 = 压力前 * (风险10日VaR值表.VAR值 / 账面价值)
   匹配条件：风险10日VaR值表.境内外 = 境内资产 AND
            风险10日VaR值表.项目分类 = 债券型基金 AND
            风险10日VaR值表.样本期限 = 3年

   压力后 = 压力前 - 变动值
   ```

4. **账面价值4（五级分类资产）**
   ```
   压力前 = SUM(整体资产明细表.账面价值)
   匹配条件：整体资产明细表.五级分类 IN (关注类, 次级类, 可疑类, 损失类) AND
            整体资产明细表.资产大类 = 固定收益类投资资产 AND 账户名称匹配

   变动值 = 压力前 * 60%
   压力后 = 压力前 - 变动值
   ```

5. **账面价值5（股票基金）**
   ```
   压力前 = SUM(整体资产明细表.账面价值)
   匹配条件：[整体资产明细表.资产小小类 = 上市普通股 OR
            整体资产明细表.资产小小类 = 证券投资基金] AND 账户名称匹配

   变动值 = 待确定计算规则
   压力后 = 压力前 - 变动值
   ```

6. **账面价值6（其他权益类）**
   ```
   压力前 = SUM(整体资产明细表.账面价值) - 账面价值5压力前
   匹配条件：整体资产明细表.资产大类 = 权益类投资资产 AND 账户名称匹配

   变动值 = 压力前 * 15%
   压力后 = 压力前 - 变动值
   ```

7. **账面价值7（投资性房地产）**
   ```
   压力前 = SUM(整体资产明细表.账面价值)
   匹配条件：整体资产明细表.资产大类 = 投资性房地产 AND 账户名称匹配
   变动值 = 压力前 * (1 - 80%) = 压力前 * 20%
   压力后 = 压力前 - 变动值
   ```

#### 2.2.2 成本收益匹配压力一汇总表（TB0011）计算规则

**汇总维度：** 按账户名称和项目类型聚合

**变动值计算：**
```
如果项目 = 固定收益类投资资产：
    变动值 = 账面价值1变动值 + 账面价值2变动值 + 账面价值3变动值 + 账面价值4变动值

如果项目 = 权益类投资资产：
    变动值 = 账面价值5变动值 + 账面价值6变动值

如果项目 = 投资性房地产：
    变动值 = 账面价值7变动值
```

#### 2.2.3 压力测试规则说明

**7条压力测试规则（用于页面悬浮提示）：**
1. 利差扩大77%：利差扩大只评估以公允价值计量的固定收益类投资资产
2. 境内债券型基金按照公司报送99%置信区间下三年VaR情况计算损失
3. 不可计算现金流的固定收益类保险资产管理产品、境外债券型基金、货币类保险资产管理产品等按照公司报送99%置信区间下境内债券型基金三年VaR值与境内债券型基金账面价值的比例计算损失
4. 固定收益类投资资产风险分类关注类及以下的资产发生违约，产生相当于其对应资产账面价值60%的损失
5. 境内外股票和基金根据公司报送99%置信区间下三年VaR值情况下跌
6. 除境内外股票和基金外的权益类投资资产按账面价值的15%计提减值损失
7. 对于按公允价值计价的上述资产，根据对应资产的账面价值乘以80%，得到压力情景下的账面价值

## 3. 数据流向说明

### 3.1 CFT模块数据流向

**主要数据流向：**
```
资产整体明细表（外部引用）→ 质押券明细表
                        ↓
资产整体明细表（外部引用）→ 资产规模及变现金额明细表（基本情景）
质押券明细表           ↗                    ↓
                                    复制并调整参数
                                         ↓
                              资产规模及变现金额明细表（压力情景）
                                         ↓
                              资产规模及变现金额汇总表
```

**详细流程说明：**
1. **基础数据获取**：从asset_details_program_design.md中的TB0016资产整体明细表获取基础资产数据
2. **质押券识别**：基于资产整体明细表生成质押券明细表
3. **基本情景生成**：结合资产整体明细表和质押券明细表，生成基本情景下的资产规模及变现金额明细表
4. **压力情景生成**：复制基本情景数据，调整变现系数等参数，生成压力情景数据
5. **数据汇总**：将明细表数据按维度汇总生成汇总表

**现金流预测相关数据流向：**
```
久期资产明细表（外部引用）→ 资产现金流预测表
现金流量表（外部引用） → 资产现金流预测表
现金流项目映射表（外部引用） → 资产现金流预测表
```

**实际现金流数据流向：**
```
现金流量表（外部引用）→ ALMCF实际发生数表
现金流项目映射表（外部引用）→ ALMCF实际发生数表
红利收入明细表（手动导入）→ ALMCF实际发生数表
固收资产现金流表（手动导入）→ ALMCF实际发生数表
                              ↓
                    本季度实际资产现金流分析
```

### 3.2 PIR模块数据流向

**成本收益匹配压力测试数据流向：**
```
久期资产明细表（外部引用）→ 成本收益匹配压力一明细表
整体资产明细表（外部引用）→ 成本收益匹配压力一明细表
风险10日VaR值表（外部引用）→ 成本收益匹配压力一明细表
                              ↓
                    成本收益匹配压力一汇总表
```

**详细流程说明：**
1. **基础数据获取**：从久期资产明细表和整体资产明细表获取资产基础数据
2. **压力测试计算**：根据7条压力测试规则计算各类资产的压力前后账面价值
3. **数据汇总**：将明细表数据按账户和项目类型汇总生成汇总表

## 4. 技术实现要点

### 4.1 数据精度要求
- **高精度金额字段**：使用decimal(30,10)，支持券面总额等高精度要求
- **一般金额字段**：使用decimal(18,2)，支持千万亿级别金额
- **比例字段**：使用decimal(10,6)，支持百万分之一精度（如质押比例、变现系数）
- **百分比字段**：使用decimal(10,4)，支持万分之一精度

### 4.2 索引设计
- **分区键**：所有表都包含accounting_period字段作为分区键
- **唯一性约束**：通过多字段组合索引保证业务唯一性
- **查询优化**：针对常用查询条件建立合适的复合索引
- **情景查询**：scenario_name字段需要建立索引以支持情景对比查询

### 4.3 外部依赖

**CFT模块外部依赖：**
- **核心依赖1**：asset_details_program_design.md中的TB0016资产整体明细表
- **核心依赖2**：adur_program_design.md中的TB0003久期资产明细表
- **核心依赖3**：cft_libility_program_design.md中的TB0020现金流量表和TB0021现金流项目映射表

**PIR模块外部依赖：**
- **核心依赖1**：asset_details_program_design.md中的TB0016资产整体明细表
- **核心依赖2**：adur_program_design.md中的TB0003久期资产明细表
- **核心依赖3**：风险10日VaR值表（需要确认具体来源和表结构）

**通用要求：**
- **字段映射**：需要确保字段名称和数据类型与外部引用表保持一致
- **数据同步**：外部表数据更新后，需要重新生成相关表数据

### 4.4 数据字典依赖

**已存在的字典（可直接引用）：**
- **情景名称**：cft_scenario_name（引用自docs/sql/cash_flow_test_program_dict.sql）
  - 01:基本情景, 02:压力情景
- **账户名称**：ast_account_name_mapping（引用自docs/sql/ast_program_dict.sql）
  - 01:传统账户, 02:分红账户, 03:万能账户, 04:独立账户, 05:资本补充债账户, 06:普通账户

**需要新建的字典：**
- **统计类型**：cft_statistics_type
  - 01:本年累计, 02:本季度累计
- **现金流项目类型**：cft_cashflow_item_type
  - 01:利息收入, 02:到期资产
- **PIR资产类别**：pir_asset_category
  - 01:固定收益类投资资产, 02:权益类投资资产, 03:投资性房地产
- **资产流动性分类**：ast_asset_liquidity_category
  - 01:高流动性资产, 02:中低流动性资产, 03:现金及流动性管理工具

**其他引用字典：**
- **资产小小类分类**：ast_asset_sub_sub_category（需要确认是否已存在）

### 4.5 特殊UI需求

**压力测试说明悬浮提示功能：**
- **适用页面**：成本收益匹配压力一明细表和汇总表的展示页面
- **触发方式**：鼠标悬停在相关字段或标题上
- **显示内容**：7条压力测试规则说明（详见2.2.3节）
- **实现方式**：前端tooltip组件或自定义悬浮层
- **样式要求**：清晰易读，支持多行文本显示

### 4.6 计算复杂度考虑
- **批量处理**：支持大批量数据的情景生成和计算
- **并行计算**：基本情景和压力情景可以并行计算
- **增量更新**：支持基于资产整体明细表变化的增量更新机制
- **压力测试计算**：PIR模块涉及复杂的压力测试计算，需要优化计算性能

