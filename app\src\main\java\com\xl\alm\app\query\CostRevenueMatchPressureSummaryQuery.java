package com.xl.alm.app.query;

import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 成本收益匹配压力一汇总表查询参数对象
 *
 * <AUTHOR> Assistant
 */
@Data
public class CostRevenueMatchPressureSummaryQuery extends BaseEntity {
    
    /**
     * 账期，格式YYYYMM（如202406）
     */
    @Pattern(regexp = "^\\d{6}$", message = "账期格式不正确，应为YYYYMM格式")
    private String accountingPeriod;
    
    /**
     * 账户名称，引用字典ast_account_name_mapping
     */
    @Size(max = 50, message = "账户名称长度不能超过50个字符")
    private String accountName;
    
    /**
     * 项目名称，建议新建字典pir_asset_category：01:固定收益类投资资产,02:权益类投资资产,03:投资性房地产
     */
    @Size(max = 50, message = "项目名称长度不能超过50个字符")
    private String itemName;
    
    /**
     * 变动值，压力测试后的价值变动金额，单位：元
     */
    private BigDecimal changeValue;
}
