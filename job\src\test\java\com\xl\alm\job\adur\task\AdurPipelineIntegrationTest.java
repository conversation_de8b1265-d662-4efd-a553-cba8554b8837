package com.xl.alm.job.adur.task;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ADUR模块流水线集成测试类
 * 按照指定顺序依次执行所有ADUR任务，每个步骤完全依赖上一步结果
 * 
 * 执行顺序：
 * 1. AdurDurationAssetDetailGenerationTask - 久期资产明细数据生成
 * 2. AdurAnnualDiscountCurveTask - 年度折现曲线计算
 * 3. AdurMonthlyDiscountCurveCalculationTask - 月度折现曲线不含价差计算
 * 4. AdurMonthlyDiscountCurveWithSpreadTask - 月度折现曲线含价差计算
 * 5. AdurMonthlyDiscountFactorWithSpreadTask - 月度折现因子含价差计算
 * 6. AdurIssueSpreadCalculationTask - 发行时点价差计算
 * 7. AdurKeyDurationDiscountCurveWithSpreadTask - 关键久期折现曲线含价差计算
 * 8. AdurKeyDurationDiscountFactorWithSpreadTask - 关键久期折现因子含价差计算
 * 9. AdurDurationIndicatorCalculationTask - 久期指标计算
 * 10. AdurDurationAssetSummaryCalculationTask - 久期资产汇总计算
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@SpringBootTest
public class AdurPipelineIntegrationTest {

    // 测试账期
    private static final String TEST_ACCOUNT_PERIOD = "202406";

    // 注入所有任务
    @Autowired
    private AdurDurationAssetDetailGenerationTask durationAssetDetailGenerationTask;

    @Autowired
    private AdurAnnualDiscountCurveTask annualDiscountCurveTask;

    @Autowired
    private AdurMonthlyDiscountCurveCalculationTask monthlyDiscountCurveCalculationTask;

    @Autowired
    private AdurMonthlyDiscountCurveWithSpreadTask monthlyDiscountCurveWithSpreadTask;

    @Autowired
    private AdurMonthlyDiscountFactorWithSpreadTask monthlyDiscountFactorWithSpreadTask;

    @Autowired
    private AdurIssueSpreadCalculationTask issueSpreadCalculationTask;

    @Autowired
    private AdurKeyDurationDiscountCurveWithSpreadTask keyDurationDiscountCurveWithSpreadTask;

    @Autowired
    private AdurKeyDurationDiscountFactorWithSpreadTask keyDurationDiscountFactorWithSpreadTask;

    @Autowired
    private AdurDurationIndicatorCalculationTask durationIndicatorCalculationTask;

    @Autowired
    private AdurDurationAssetSummaryCalculationTask durationAssetSummaryCalculationTask;

    /**
     * ADUR模块完整流水线集成测试
     * 按照业务逻辑顺序依次执行所有任务
     */
    @Test
    public void testAdurPipelineIntegration() {
        log.info("=== 开始执行ADUR模块完整流水线集成测试，账期：{} ===", TEST_ACCOUNT_PERIOD);
        long totalStartTime = System.currentTimeMillis();

        try {
          // 步骤1：久期资产明细数据生成
            executeStep(1, "久期资产明细数据生成", () ->
                durationAssetDetailGenerationTask.execute(TEST_ACCOUNT_PERIOD));

            // 步骤2：年度折现曲线计算
            executeStep(2, "年度折现曲线计算", () -> 
                annualDiscountCurveTask.execute(TEST_ACCOUNT_PERIOD));

            // 步骤3：月度折现曲线不含价差计算
            executeStep(3, "月度折现曲线不含价差计算", () -> 
                monthlyDiscountCurveCalculationTask.execute(TEST_ACCOUNT_PERIOD));

            // 步骤4：月度折现曲线含价差计算
            executeStep(4, "月度折现曲线含价差计算", () -> 
                monthlyDiscountCurveWithSpreadTask.execute(TEST_ACCOUNT_PERIOD));

            // 步骤5：月度折现因子含价差计算
            executeStep(5, "月度折现因子含价差计算", () -> 
                monthlyDiscountFactorWithSpreadTask.execute(TEST_ACCOUNT_PERIOD));

            // 步骤6：发行时点价差计算
            executeStep(6, "发行时点价差计算", () -> 
                issueSpreadCalculationTask.execute(TEST_ACCOUNT_PERIOD));

            // 步骤7：关键久期折现曲线含价差计算
            executeStep(7, "关键久期折现曲线含价差计算", () -> 
                keyDurationDiscountCurveWithSpreadTask.execute(TEST_ACCOUNT_PERIOD));

            // 步骤8：关键久期折现因子含价差计算
            executeStep(8, "关键久期折现因子含价差计算", () -> 
                keyDurationDiscountFactorWithSpreadTask.execute(TEST_ACCOUNT_PERIOD));

            // 步骤9：久期指标计算
            executeStep(9, "久期指标计算", () -> 
                durationIndicatorCalculationTask.execute(TEST_ACCOUNT_PERIOD));

            // 步骤10：久期资产汇总计算
            executeStep(10, "久期资产汇总计算", () -> 
                durationAssetSummaryCalculationTask.execute(TEST_ACCOUNT_PERIOD));

            long totalEndTime = System.currentTimeMillis();
            log.info("=== ADUR模块完整流水线集成测试执行成功！总耗时：{}ms ===", 
                    (totalEndTime - totalStartTime));

        } catch (Exception e) {
            long totalEndTime = System.currentTimeMillis();
            log.error("=== ADUR模块完整流水线集成测试执行失败！总耗时：{}ms ===", 
                    (totalEndTime - totalStartTime), e);
            fail("ADUR流水线集成测试失败：" + e.getMessage());
        }
    }

    /**
     * 执行单个步骤
     *
     * @param stepNumber 步骤编号
     * @param stepName 步骤名称
     * @param taskExecutor 任务执行器
     */
    private void executeStep(int stepNumber, String stepName, TaskExecutor taskExecutor) {
        log.info("--- 步骤{}: 开始执行{} ---", stepNumber, stepName);
        long stepStartTime = System.currentTimeMillis();

        try {
            boolean result = taskExecutor.execute();
            long stepEndTime = System.currentTimeMillis();

            if (result) {
                log.info("--- 步骤{}: {}执行成功，耗时：{}ms ---", 
                        stepNumber, stepName, (stepEndTime - stepStartTime));
            } else {
                log.error("--- 步骤{}: {}执行失败，耗时：{}ms ---", 
                        stepNumber, stepName, (stepEndTime - stepStartTime));
                fail(String.format("步骤%d：%s执行失败", stepNumber, stepName));
            }

        } catch (Exception e) {
            long stepEndTime = System.currentTimeMillis();
            log.error("--- 步骤{}: {}执行异常，耗时：{}ms ---", 
                    stepNumber, stepName, (stepEndTime - stepStartTime), e);
            fail(String.format("步骤%d：%s执行异常：%s", stepNumber, stepName, e.getMessage()));
        }
    }

    /**
     * 任务执行器函数式接口
     */
    @FunctionalInterface
    private interface TaskExecutor {
        boolean execute();
    }


}
