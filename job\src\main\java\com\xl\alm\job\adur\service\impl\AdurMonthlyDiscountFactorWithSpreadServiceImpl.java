package com.xl.alm.job.adur.service.impl;

import com.xl.alm.job.adur.entity.AdurMonthlyDiscountCurveWithSpreadEntity;
import com.xl.alm.job.adur.entity.AdurMonthlyDiscountFactorWithSpreadEntity;
import com.xl.alm.job.adur.mapper.AdurMonthlyDiscountCurveWithSpreadMapper;
import com.xl.alm.job.adur.mapper.AdurMonthlyDiscountFactorWithSpreadMapper;
import com.xl.alm.job.adur.service.AdurMonthlyDiscountFactorWithSpreadService;
import com.xl.alm.job.adur.util.TermDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * ADUR月度折现因子含价差计算服务实现类
 * 对应用例：UC0007 计算月度折现因子含价差
 *
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class AdurMonthlyDiscountFactorWithSpreadServiceImpl implements AdurMonthlyDiscountFactorWithSpreadService {

    @Autowired
    private AdurMonthlyDiscountCurveWithSpreadMapper monthlyDiscountCurveWithSpreadMapper;

    @Autowired
    private AdurMonthlyDiscountFactorWithSpreadMapper monthlyDiscountFactorWithSpreadMapper;

    /**
     * 计算月度折现因子含价差
     *
     * @param accountPeriod 账期，格式YYYYMM
     * @return 处理结果，true表示成功，false表示失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean calculateMonthlyDiscountFactorWithSpread(String accountPeriod) {
        log.info("开始执行月度折现因子含价差计算，账期：{}", accountPeriod);
        try {
            // 步骤1：加载基础数据
            List<AdurMonthlyDiscountCurveWithSpreadEntity> monthlyDiscountCurveWithSpreadList = loadMonthlyDiscountCurveWithSpreadData(accountPeriod);
            log.info("加载月度折现曲线含价差数据完成，共加载{}条记录", monthlyDiscountCurveWithSpreadList.size());

            // 步骤2：计算折现因子
            List<AdurMonthlyDiscountFactorWithSpreadEntity> monthlyDiscountFactorWithSpreadList = calculateMonthlyDiscountFactorWithSpreadData(
                    accountPeriod, monthlyDiscountCurveWithSpreadList);
            log.info("计算月度折现因子含价差数据完成，共计算{}条记录", monthlyDiscountFactorWithSpreadList.size());

            // 步骤3：数据入表
            if (!CollectionUtils.isEmpty(monthlyDiscountFactorWithSpreadList)) {
                // 先删除原有数据
                monthlyDiscountFactorWithSpreadMapper.deleteByAccountPeriod(accountPeriod);
                log.info("删除账期{}的原有月度折现因子含价差数据", accountPeriod);

                // 批量插入新数据
                int insertCount = monthlyDiscountFactorWithSpreadMapper.batchInsertMonthlyDiscountFactorWithSpread(monthlyDiscountFactorWithSpreadList);
                log.info("批量插入月度折现因子含价差数据完成，插入{}条记录", insertCount);
            }

            log.info("月度折现因子含价差计算完成，账期：{}", accountPeriod);
            return true;

        } catch (Exception e) {
            log.error("月度折现因子含价差计算失败，账期：{}", accountPeriod, e);
            return false;
        }
    }

    /**
     * 加载月度折现曲线含价差数据
     *
     * @param accountPeriod 账期
     * @return 月度折现曲线含价差列表
     */
    private List<AdurMonthlyDiscountCurveWithSpreadEntity> loadMonthlyDiscountCurveWithSpreadData(String accountPeriod) {
        List<AdurMonthlyDiscountCurveWithSpreadEntity> monthlyDiscountCurveWithSpreadList = monthlyDiscountCurveWithSpreadMapper.selectByAccountPeriod(accountPeriod);
        
        if (CollectionUtils.isEmpty(monthlyDiscountCurveWithSpreadList)) {
            log.warn("未找到账期{}的月度折现曲线含价差数据", accountPeriod);
            return new ArrayList<>();
        }
        
        return monthlyDiscountCurveWithSpreadList;
    }

    /**
     * 计算月度折现因子含价差数据
     *
     * @param accountPeriod 账期
     * @param monthlyDiscountCurveWithSpreadList 月度折现曲线含价差列表
     * @return 月度折现因子含价差列表
     */
    private List<AdurMonthlyDiscountFactorWithSpreadEntity> calculateMonthlyDiscountFactorWithSpreadData(
            String accountPeriod,
            List<AdurMonthlyDiscountCurveWithSpreadEntity> monthlyDiscountCurveWithSpreadList) {
        
        List<AdurMonthlyDiscountFactorWithSpreadEntity> resultList = new ArrayList<>();
        
        // 遍历每个月度折现曲线含价差记录
        for (AdurMonthlyDiscountCurveWithSpreadEntity curveEntity : monthlyDiscountCurveWithSpreadList) {
            try {
                // 创建对应的折现因子实体
                AdurMonthlyDiscountFactorWithSpreadEntity factorEntity = createDiscountFactorEntity(accountPeriod, curveEntity);
                
                // 计算0-600期的折现因子
                calculateDiscountFactors(factorEntity, curveEntity);
                
                resultList.add(factorEntity);
                
            } catch (Exception e) {
                log.error("计算资产{}的月度折现因子含价差失败", curveEntity.getAssetNumber(), e);
                // 继续处理其他记录
            }
        }
        
        return resultList;
    }

    /**
     * 创建折现因子实体
     *
     * @param accountPeriod 账期
     * @param curveEntity 月度折现曲线含价差实体
     * @return 月度折现因子含价差实体
     */
    private AdurMonthlyDiscountFactorWithSpreadEntity createDiscountFactorEntity(
            String accountPeriod,
            AdurMonthlyDiscountCurveWithSpreadEntity curveEntity) {
        
        AdurMonthlyDiscountFactorWithSpreadEntity factorEntity = new AdurMonthlyDiscountFactorWithSpreadEntity();
        
        // 继承月度折现曲线含价差的所有维度字段
        factorEntity.setAccountPeriod(accountPeriod);
        factorEntity.setDurationType(curveEntity.getDurationType());
        factorEntity.setBasisPointType(curveEntity.getBasisPointType());
        factorEntity.setDateType(curveEntity.getDateType());
        factorEntity.setDate(curveEntity.getDate());
        factorEntity.setSpreadType(curveEntity.getSpreadType());
        factorEntity.setSpread(curveEntity.getSpread());
        factorEntity.setCurveSubCategory(curveEntity.getCurveSubCategory());
        factorEntity.setAssetNumber(curveEntity.getAssetNumber());
        factorEntity.setAccountName(curveEntity.getAccountName());
        factorEntity.setAssetName(curveEntity.getAssetName());
        factorEntity.setSecurityCode(curveEntity.getSecurityCode());
        factorEntity.setCurveId(curveEntity.getCurveId());
        
        // 设置审计字段
        factorEntity.setCreateTime(new Date());
        factorEntity.setCreateBy("SYSTEM");
        factorEntity.setUpdateTime(new Date());
        factorEntity.setUpdateBy("SYSTEM");
        
        return factorEntity;
    }

    /**
     * 计算折现因子
     *
     * @param factorEntity 折现因子实体
     * @param curveEntity 月度折现曲线含价差实体
     */
    private void calculateDiscountFactors(
            AdurMonthlyDiscountFactorWithSpreadEntity factorEntity,
            AdurMonthlyDiscountCurveWithSpreadEntity curveEntity) {

        // 获取曲线实体的期限数据
        Map<Integer, BigDecimal> curveTermValues = TermDataUtil.parseTermValues(curveEntity.getMonthlyDiscountRateWithSpreadSet());
        Map<Integer, BigDecimal> factorTermValues = new HashMap<>();

        // 计算0-600期的折现因子
        for (int termIndex = 0; termIndex <= 600; termIndex++) {
            BigDecimal yieldRate = curveTermValues.get(termIndex);

            if (yieldRate != null ) {
                // 计算公式：期限X的折现因子 = 1/(1+月度折现曲线表含价差.期限X)^(X/12)
                BigDecimal discountFactor = calculateDiscountFactor(yieldRate, termIndex);
                factorTermValues.put(termIndex, discountFactor);
            } //else {
                // 如果收益率为空或负数，则折现因子设置为0（确保JSON包含所有期限）
                //factorTermValues.put(termIndex, BigDecimal.ZERO);
            //}
        }

        // 将计算结果转换为JSON格式并设置到实体（确保包含0-600所有期限）
        String factorJson = TermDataUtil.createCompleteTermJson(factorTermValues);
        factorEntity.setMonthlyDiscountFactorSet(factorJson);
    }

    /**
     * 计算单个期限的折现因子
     *
     * @param yieldRate 收益率
     * @param termIndex 期限索引(月份数)
     * @return 折现因子
     */
    private BigDecimal calculateDiscountFactor(BigDecimal yieldRate, int termIndex) {
        try {
            // 计算公式：期限X的折现因子 = 1/(1+收益率)^(X/12)

            // 如果期限为0，折现因子为1
            if (termIndex == 0) {
                return BigDecimal.ONE;
            }

            // 计算 (1 + 收益率)
            BigDecimal onePlusRate = BigDecimal.ONE.add(yieldRate);

            // 使用高精度幂运算：(1 + 收益率)^(X/12)
            BigDecimal exponent = new BigDecimal(termIndex).divide(new BigDecimal("12"), 15, RoundingMode.HALF_UP);
            BigDecimal powerResult = bigDecimalPow(onePlusRate, exponent, 15);

            // 计算 1 / (1 + 收益率)^(X/12)
            BigDecimal discountFactor = BigDecimal.ONE.divide(powerResult, 15, RoundingMode.HALF_UP);

            // 保留10位小数
            return discountFactor.setScale(10, RoundingMode.HALF_UP);

        } catch (Exception e) {
            log.error("计算期限{}的折现因子失败，收益率：{}", termIndex, yieldRate, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 高精度BigDecimal幂运算
     * 使用对数和指数函数计算 base^exponent
     *
     * @param base 底数
     * @param exponent 指数
     * @param scale 计算精度
     * @return base^exponent
     */
    private BigDecimal bigDecimalPow(BigDecimal base, BigDecimal exponent, int scale) {
        // 对于简单情况的优化
        if (exponent.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ONE;
        }
        if (exponent.compareTo(BigDecimal.ONE) == 0) {
            return base;
        }
        if (base.compareTo(BigDecimal.ONE) == 0) {
            return BigDecimal.ONE;
        }

        try {
            // 使用对数和指数函数：base^exponent = e^(exponent * ln(base))
            double baseDouble = base.doubleValue();
            double exponentDouble = exponent.doubleValue();

            // 检查是否在合理范围内
            if (baseDouble <= 0) {
                throw new ArithmeticException("底数必须大于0");
            }

            // 使用高精度计算
            double logBase = Math.log(baseDouble);
            double result = Math.exp(exponentDouble * logBase);

            // 转换回BigDecimal并保持精度
            return new BigDecimal(result).setScale(scale, RoundingMode.HALF_UP);

        } catch (Exception e) {
            log.warn("高精度幂运算失败，使用标准Math.pow作为备选方案", e);
            // 备选方案：使用标准Math.pow
            double result = Math.pow(base.doubleValue(), exponent.doubleValue());
            return BigDecimal.valueOf(result).setScale(scale, RoundingMode.HALF_UP);
        }
    }
}
