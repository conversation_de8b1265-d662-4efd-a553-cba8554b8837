-- 现金流测试与投资收益率预测系统数据表DDL
-- 基于 docs/design/cft_pir_asset_cashflow_design.md 中的表集章节生成
-- 数据库：MySQL 8.0
-- 字符集：utf8

-- =============================================
-- TB0001 - 质押券明细表
-- =============================================
DROP TABLE IF EXISTS `t_cft_pledge_security_detail`;
CREATE TABLE `t_cft_pledge_security_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM（如202406）',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称，引用字典ast_account_name_mapping',
  `asset_name` varchar(100) NOT NULL COMMENT '资产名称，质押券资产名称',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码，质押券证券标识代码',
  `face_value_total` decimal(28,10) DEFAULT 0.********** COMMENT '券面总额，质押券券面总额，单位：元',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_period_account_security` (`accounting_period`, `account_name`, `security_code`),
  KEY `idx_accounting_period` (`accounting_period`),
  KEY `idx_account_name` (`account_name`),
  KEY `idx_security_code` (`security_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='质押券明细表';

-- =============================================
-- TB0002 - 资产规模及变现金额明细表
-- =============================================
DROP TABLE IF EXISTS `t_cft_asset_scale_liquidation_detail`;
CREATE TABLE `t_cft_asset_scale_liquidation_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM（如202406）',
  `scenario_name` varchar(20) NOT NULL COMMENT '情景名称，引用字典cft_scenario_name',
  `asset_number` varchar(20) NOT NULL COMMENT '资产编号，来源于整体资产明细表.资产编号',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称，来源于整体资产明细表.账户名称，引用字典ast_account_name_mapping',
  `asset_name` varchar(100) DEFAULT NULL COMMENT '资产名称，来源于整体资产明细表.资产名称',
  `security_code` varchar(20) DEFAULT NULL COMMENT '证券代码，来源于整体资产明细表.证券代码',
  `holding_face_value` decimal(30,10) DEFAULT 0.********** COMMENT '持仓面值，来源于整体资产明细表.持仓面值',
  `book_balance` decimal(30,10) DEFAULT 0.********** COMMENT '账面余额，来源于整体资产明细表.账面余额',
  `adjusted_maturity_date` date DEFAULT NULL COMMENT '调整到期日，来源于整体资产明细表.调整到期日',
  `calculable_cashflow_flag` varchar(5) DEFAULT '0' COMMENT '可计算现金流固收资产标识，来源于整体资产明细表.可计算现金流固收资产标识',
  `asset_liquidity_category` varchar(20) DEFAULT NULL COMMENT '资产流动性分类，来源于整体资产明细表.资产流动性分类，01:高流动性资产,02:中低流动性资产,03:现金及流动性管理工具',
  `liquidation_coefficient` decimal(10,6) DEFAULT 0.000000 COMMENT '变现系数，来源于整体资产明细表.变现系数',
  `pledge_flag` varchar(5) DEFAULT '0' COMMENT '质押标识，通过质押券明细表匹配确定',
  `pledge_ratio` decimal(10,6) DEFAULT 0.000000 COMMENT '质押比例，质押券面额/持仓面值',
  `evaluation_time_ms1` decimal(30,10) DEFAULT 0.********** COMMENT '评估时点MS1（规模-全量券），等于账面余额',
  `future_q1_ms1` decimal(30,10) DEFAULT 0.********** COMMENT '未来第一季度末MS1（规模-全量券）',
  `future_q2_ms1` decimal(30,10) DEFAULT 0.********** COMMENT '未来第二季度末MS1（规模-全量券）',
  `future_q3_ms1` decimal(30,10) DEFAULT 0.********** COMMENT '未来第三季度末MS1（规模-全量券）',
  `future_q4_ms1` decimal(30,10) DEFAULT 0.********** COMMENT '未来第四季度末MS1（规模-全量券）',
  `future_y2_ms1` decimal(30,10) DEFAULT 0.********** COMMENT '未来第二年末MS1（规模-全量券）',
  `future_y3_ms1` decimal(30,10) DEFAULT 0.********** COMMENT '未来第三年末MS1（规模-全量券）',
  `evaluation_time_ms2` decimal(30,10) DEFAULT 0.********** COMMENT '评估时点MS2（规模-质押券），等于账面余额*质押比例',
  `future_q1_ms2` decimal(30,10) DEFAULT 0.********** COMMENT '未来第一季度末MS2（规模-质押券）',
  `future_q2_ms2` decimal(30,10) DEFAULT 0.********** COMMENT '未来第二季度末MS2（规模-质押券）',
  `future_q3_ms2` decimal(30,10) DEFAULT 0.********** COMMENT '未来第三季度末MS2（规模-质押券）',
  `future_q4_ms2` decimal(30,10) DEFAULT 0.********** COMMENT '未来第四季度末MS2（规模-质押券）',
  `future_y2_ms2` decimal(30,10) DEFAULT 0.********** COMMENT '未来第二年末MS2（规模-质押券）',
  `future_y3_ms2` decimal(30,10) DEFAULT 0.********** COMMENT '未来第三年末MS2（规模-质押券）',
  `future_q1_ma` decimal(30,10) DEFAULT 0.********** COMMENT '未来第一季度末MA（变现金额）',
  `future_q2_ma` decimal(30,10) DEFAULT 0.********** COMMENT '未来第二季度末MA（变现金额）',
  `future_q3_ma` decimal(30,10) DEFAULT 0.********** COMMENT '未来第三季度末MA（变现金额）',
  `future_q4_ma` decimal(30,10) DEFAULT 0.********** COMMENT '未来第四季度末MA（变现金额）',
  `future_y2_ma` decimal(30,10) DEFAULT 0.********** COMMENT '未来第二年末MA（变现金额）',
  `future_y3_ma` decimal(30,10) DEFAULT 0.********** COMMENT '未来第三年末MA（变现金额）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_period_scenario_asset_account` (`accounting_period`, `scenario_name`, `asset_number`, `account_name`, `security_code`),
  KEY `idx_accounting_period` (`accounting_period`),
  KEY `idx_scenario_name` (`scenario_name`),
  KEY `idx_account_name` (`account_name`),
  KEY `idx_asset_number` (`asset_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资产规模及变现金额明细表';

-- =============================================
-- TB0003 - 资产规模及变现金额汇总表
-- =============================================
DROP TABLE IF EXISTS `t_cft_asset_scale_liquidation_summary`;
CREATE TABLE `t_cft_asset_scale_liquidation_summary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM（如202406）',
  `scenario_name` varchar(20) NOT NULL COMMENT '情景名称，引用字典cft_scenario_name',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称，引用字典ast_account_name_mapping',
  `asset_liquidity_category` varchar(50) NOT NULL COMMENT '资产流动性分类，01:高流动性资产,02:中低流动性资产,03:现金及流动性管理工具',
  `evaluation_time_ms1` decimal(30,10) DEFAULT 0.********** COMMENT '评估时点MS1（规模-全量券）',
  `future_q1_ms1` decimal(30,10) DEFAULT 0.********** COMMENT '未来第一季度末MS1（规模-全量券）',
  `future_q2_ms1` decimal(30,10) DEFAULT 0.********** COMMENT '未来第二季度末MS1（规模-全量券）',
  `future_q3_ms1` decimal(30,10) DEFAULT 0.********** COMMENT '未来第三季度末MS1（规模-全量券）',
  `future_q4_ms1` decimal(30,10) DEFAULT 0.********** COMMENT '未来第四季度末MS1（规模-全量券）',
  `future_y2_ms1` decimal(30,10) DEFAULT 0.********** COMMENT '未来第二年末MS1（规模-全量券）',
  `future_y3_ms1` decimal(30,10) DEFAULT 0.********** COMMENT '未来第三年末MS1（规模-全量券）',
  `evaluation_time_ms2` decimal(30,10) DEFAULT 0.********** COMMENT '评估时点MS2（规模-质押券）',
  `future_q1_ms2` decimal(30,10) DEFAULT 0.********** COMMENT '未来第一季度末MS2（规模-质押券）',
  `future_q2_ms2` decimal(30,10) DEFAULT 0.********** COMMENT '未来第二季度末MS2（规模-质押券）',
  `future_q3_ms2` decimal(30,10) DEFAULT 0.********** COMMENT '未来第三季度末MS2（规模-质押券）',
  `future_q4_ms2` decimal(30,10) DEFAULT 0.********** COMMENT '未来第四季度末MS2（规模-质押券）',
  `future_y2_ms2` decimal(30,10) DEFAULT 0.********** COMMENT '未来第二年末MS2（规模-质押券）',
  `future_y3_ms2` decimal(30,10) DEFAULT 0.********** COMMENT '未来第三年末MS2（规模-质押券）',
  `future_q1_ma` decimal(30,10) DEFAULT 0.********** COMMENT '未来第一季度末MA（变现金额）',
  `future_q2_ma` decimal(30,10) DEFAULT 0.********** COMMENT '未来第二季度末MA（变现金额）',
  `future_q3_ma` decimal(30,10) DEFAULT 0.********** COMMENT '未来第三季度末MA（变现金额）',
  `future_q4_ma` decimal(30,10) DEFAULT 0.********** COMMENT '未来第四季度末MA（变现金额）',
  `future_y2_ma` decimal(30,10) DEFAULT 0.********** COMMENT '未来第二年末MA（变现金额）',
  `future_y3_ma` decimal(30,10) DEFAULT 0.********** COMMENT '未来第三年末MA（变现金额）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_period_scenario_account_category` (`accounting_period`, `scenario_name`, `account_name`, `asset_liquidity_category`),
  KEY `idx_accounting_period` (`accounting_period`),
  KEY `idx_scenario_name` (`scenario_name`),
  KEY `idx_account_name` (`account_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资产规模及变现金额汇总表';

-- =============================================
-- TB0004 - 资产现金流预测表
-- =============================================
DROP TABLE IF EXISTS `t_cft_asset_cashflow_forecast`;
CREATE TABLE `t_cft_asset_cashflow_forecast` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM（如202406）',
  `scenario_name` varchar(20) NOT NULL COMMENT '情景名称，引用字典cft_scenario_name',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称，引用字典ast_account_name_mapping',
  `item_name` varchar(50) NOT NULL COMMENT '现金流项目类型，建议新建字典cft_cashflow_item_type：01:利息收入,02:到期资产',
  `future_q1` decimal(28,10) DEFAULT 0.********** COMMENT '未来第一季度现金流金额，单位：元',
  `future_q2` decimal(28,10) DEFAULT 0.********** COMMENT '未来第二季度现金流金额，单位：元',
  `future_q3` decimal(28,10) DEFAULT 0.********** COMMENT '未来第三季度现金流金额，单位：元',
  `future_q4` decimal(28,10) DEFAULT 0.********** COMMENT '未来第四季度现金流金额，单位：元',
  `future_y2_remaining` decimal(28,10) DEFAULT 0.********** COMMENT '未来第二年剩余季度现金流金额，单位：元',
  `future_y3` decimal(28,10) DEFAULT 0.********** COMMENT '未来第三年现金流金额，单位：元',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_period_scenario_account_item` (`accounting_period`, `scenario_name`, `account_name`, `item_name`),
  KEY `idx_accounting_period` (`accounting_period`),
  KEY `idx_scenario_name` (`scenario_name`),
  KEY `idx_account_name` (`account_name`),
  KEY `idx_item_name` (`item_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='资产现金流预测表';

-- =============================================
-- TB0007 - 红利收入明细表
-- =============================================
DROP TABLE IF EXISTS `t_cft_actual_asset_cashflow_dividend`;
CREATE TABLE `t_cft_actual_asset_cashflow_dividend` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM（如202406）',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称，引用字典ast_account_name_mapping',
  `asset_sub_sub_category` varchar(50) NOT NULL COMMENT '资产小小类分类，引用字典ast_asset_sub_sub_category',
  `account_set_name` varchar(100) NOT NULL COMMENT '账套名称，具体账套标识',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码，证券唯一标识',
  `security_name` varchar(100) NOT NULL COMMENT '证券名称，证券具体名称',
  `voucher_date` date NOT NULL COMMENT '凭证日期，红利收入凭证日期',
  `received_amount` decimal(30,10) NOT NULL DEFAULT 0.********** COMMENT '到账金额，红利收入到账金额，单位：元',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_period_account_category_set_security_date_amount` (`accounting_period`, `account_name`, `asset_sub_sub_category`, `account_set_name`, `security_code`, `voucher_date`, `received_amount`),
  KEY `idx_accounting_period` (`accounting_period`),
  KEY `idx_account_name` (`account_name`),
  KEY `idx_asset_sub_sub_category` (`asset_sub_sub_category`),
  KEY `idx_security_code` (`security_code`),
  KEY `idx_voucher_date` (`voucher_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='红利收入明细表';

-- =============================================
-- TB0008 - 固收资产现金流表
-- =============================================
DROP TABLE IF EXISTS `t_cft_actual_asset_cashflow_fixed`;
CREATE TABLE `t_cft_actual_asset_cashflow_fixed` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM（如202406）',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称，引用字典ast_account_name_mapping',
  `cash_flow_date` date NOT NULL COMMENT '现金流日期，现金流发生日期',
  `security_code` varchar(20) NOT NULL COMMENT '证券代码，证券唯一标识',
  `security_name` varchar(100) NOT NULL COMMENT '证券简称，证券名称',
  `cashflow_interest` decimal(30,10) DEFAULT 0.********** COMMENT '现金流付息，付息现金流金额，单位：元',
  `cashflow_principal` decimal(30,10) DEFAULT 0.********** COMMENT '现金流本金，本金现金流金额，单位：元',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_period_account_date_security_amount` (`accounting_period`, `account_name`, `cash_flow_date`, `security_code`, `cashflow_interest`, `cashflow_principal`),
  KEY `idx_accounting_period` (`accounting_period`),
  KEY `idx_account_name` (`account_name`),
  KEY `idx_cash_flow_date` (`cash_flow_date`),
  KEY `idx_security_code` (`security_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='固收资产现金流表';

-- =============================================
-- TB0009 - ALMCF实际发生数表
-- =============================================
DROP TABLE IF EXISTS `t_cft_actual_asset_cashflow_almcf`;
CREATE TABLE `t_cft_actual_asset_cashflow_almcf` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM（如202406）',
  `item_name` varchar(100) NOT NULL COMMENT '现金流项目名称',
  `statistics_type` varchar(20) NOT NULL COMMENT '统计类型，建议新建字典cft_statistics_type：01:本年累计,02:本季度累计',
  `company_overall` decimal(30,10) DEFAULT 0.********** COMMENT '公司整体现金流金额，单位：元',
  `general_account` decimal(30,10) DEFAULT 0.********** COMMENT '普通账户现金流金额，单位：元',
  `traditional_account` decimal(30,10) DEFAULT 0.********** COMMENT '传统账户现金流金额，单位：元',
  `participating_account` decimal(30,10) DEFAULT 0.********** COMMENT '分红账户现金流金额，单位：元',
  `universal_account` decimal(30,10) DEFAULT 0.********** COMMENT '万能账户现金流金额，单位：元',
  `unit_linked_account` decimal(30,10) DEFAULT 0.********** COMMENT '投连账户现金流金额，单位：元',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_period_item_statistics` (`accounting_period`, `item_name`, `statistics_type`),
  KEY `idx_accounting_period` (`accounting_period`),
  KEY `idx_item_name` (`item_name`),
  KEY `idx_statistics_type` (`statistics_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ALMCF实际发生数表';

-- =============================================
-- TB0010 - 成本收益匹配压力一明细表
-- =============================================
DROP TABLE IF EXISTS `t_pir_cost_revenue_match_pressure_detail`;
CREATE TABLE `t_pir_cost_revenue_match_pressure_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM（如202406）',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称，引用字典ast_account_name_mapping',
  `book_value_1_before_stress` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值1压力前，利差久期资产压力前账面价值，单位：元',
  `book_value_1_after_stress` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值1压力后，利差久期资产压力后账面价值，单位：元',
  `book_value_1_change` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值1变动值，利差久期资产价值变动金额，单位：元',
  `book_value_2_before_stress` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值2压力前，债券型基金压力前账面价值，单位：元',
  `book_value_2_after_stress` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值2压力后，债券型基金压力后账面价值，单位：元',
  `book_value_2_change` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值2变动值，债券型基金价值变动金额，单位：元',
  `book_value_3_before_stress` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值3压力前，固收资管产品压力前账面价值，单位：元',
  `book_value_3_after_stress` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值3压力后，固收资管产品压力后账面价值，单位：元',
  `book_value_3_change` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值3变动值，固收资管产品价值变动金额，单位：元',
  `book_value_4_before_stress` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值4压力前，五级分类资产压力前账面价值，单位：元',
  `book_value_4_after_stress` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值4压力后，五级分类资产压力后账面价值，单位：元',
  `book_value_4_change` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值4变动值，五级分类资产价值变动金额，单位：元',
  `book_value_5_before_stress` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值5压力前，股票基金压力前账面价值，单位：元',
  `book_value_5_after_stress` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值5压力后，股票基金压力后账面价值，单位：元',
  `book_value_5_change` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值5变动值，股票基金价值变动金额，单位：元',
  `book_value_6_before_stress` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值6压力前，其他权益类压力前账面价值，单位：元',
  `book_value_6_after_stress` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值6压力后，其他权益类压力后账面价值，单位：元',
  `book_value_6_change` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值6变动值，其他权益类价值变动金额，单位：元',
  `book_value_7_before_stress` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值7压力前，投资性房地产压力前账面价值，单位：元',
  `book_value_7_after_stress` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值7压力后，投资性房地产压力后账面价值，单位：元',
  `book_value_7_change` decimal(28,10) DEFAULT 0.********** COMMENT '账面价值7变动值，投资性房地产价值变动金额，单位：元',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_period_account` (`accounting_period`, `account_name`),
  KEY `idx_accounting_period` (`accounting_period`),
  KEY `idx_account_name` (`account_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='成本收益匹配压力一明细表';

-- =============================================
-- TB0011 - 成本收益匹配压力一汇总表
-- =============================================
DROP TABLE IF EXISTS `t_pir_cost_revenue_match_pressure_summary`;
CREATE TABLE `t_pir_cost_revenue_match_pressure_summary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `accounting_period` varchar(6) NOT NULL COMMENT '账期，格式YYYYMM（如202406）',
  `account_name` varchar(50) NOT NULL COMMENT '账户名称，引用字典ast_account_name_mapping',
  `item_name` varchar(50) NOT NULL COMMENT '项目名称，建议新建字典pir_asset_category：01:固定收益类投资资产,02:权益类投资资产,03:投资性房地产',
  `change_value` decimal(28,10) DEFAULT 0.********** COMMENT '变动值，压力测试后的价值变动金额，单位：元',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除，0:否，1:是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_period_account_item` (`accounting_period`, `account_name`, `item_name`),
  KEY `idx_accounting_period` (`accounting_period`),
  KEY `idx_account_name` (`account_name`),
  KEY `idx_item_name` (`item_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='成本收益匹配压力一汇总表';
