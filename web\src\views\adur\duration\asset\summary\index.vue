<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountPeriod">
        <el-input
          v-model="queryParams.accountPeriod"
          placeholder="请输入账期"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账户名称" prop="accountName">
        <el-select
          v-model="queryParams.accountName"
          placeholder="请选择账户名称"
          clearable
          size="small"
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.adur_account_name"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['adur:duration:asset:summary:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['adur:duration:asset:summary:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['adur:duration:asset:summary:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['adur:duration:asset:summary:export']"
        >导出</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="durationAssetSummaryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountPeriod" width="100" />
      <el-table-column label="账户名称" align="center" prop="accountName" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.adur_account_name" :value="scope.row.accountName"/>
        </template>
      </el-table-column>
      <el-table-column label="市值" align="center" prop="marketValue" width="120" />
      <el-table-column label="账面余额" align="center" prop="bookBalance" width="120" />
      <el-table-column label="账面价值" align="center" prop="bookValue" width="120" />

      <!-- 账面价值σ系列字段 -->
      <el-table-column label="账面价值σ0%" align="center" prop="bookValueSigma0" width="120" />
      <el-table-column label="账面价值σ9%" align="center" prop="bookValueSigma9" width="120" />
      <el-table-column label="账面价值σ17%" align="center" prop="bookValueSigma17" width="120" />
      <el-table-column label="账面价值σ77%" align="center" prop="bookValueSigma77" width="120" />

      <el-table-column label="评估时点到期收益率" align="center" prop="evalMaturityYield" width="150" />
      <el-table-column label="评估时点资产现值" align="center" prop="evalPresentValue" width="150" />
      <el-table-column label="资产修正久期" align="center" prop="assetModifiedDuration" width="120" />
      <el-table-column label="资产有效久期" align="center" prop="assetEffectiveDuration" width="120" />

      <!-- DV10系列字段 -->
      <el-table-column label="DV10_0" align="center" prop="dv100" width="100" />
      <el-table-column label="DV10_0.5" align="center" prop="dv1005" width="100" />
      <el-table-column label="DV10_1" align="center" prop="dv101" width="100" />
      <el-table-column label="DV10_2" align="center" prop="dv102" width="100" />
      <el-table-column label="DV10_3" align="center" prop="dv103" width="100" />
      <el-table-column label="DV10_4" align="center" prop="dv104" width="100" />
      <el-table-column label="DV10_5" align="center" prop="dv105" width="100" />
      <el-table-column label="DV10_6" align="center" prop="dv106" width="100" />
      <el-table-column label="DV10_7" align="center" prop="dv107" width="100" />
      <el-table-column label="DV10_8" align="center" prop="dv108" width="100" />
      <el-table-column label="DV10_10" align="center" prop="dv1010" width="100" />
      <el-table-column label="DV10_12" align="center" prop="dv1012" width="100" />
      <el-table-column label="DV10_15" align="center" prop="dv1015" width="100" />
      <el-table-column label="DV10_20" align="center" prop="dv1020" width="100" />
      <el-table-column label="DV10_25" align="center" prop="dv1025" width="100" />
      <el-table-column label="DV10_30" align="center" prop="dv1030" width="100" />
      <el-table-column label="DV10_35" align="center" prop="dv1035" width="100" />
      <el-table-column label="DV10_40" align="center" prop="dv1040" width="100" />
      <el-table-column label="DV10_45" align="center" prop="dv1045" width="100" />
      <el-table-column label="DV10_50" align="center" prop="dv1050" width="100" />

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['adur:duration:asset:summary:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['adur:duration:asset:summary:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改久期资产结果汇总表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="账期" prop="accountPeriod">
              <el-input v-model="form.accountPeriod" placeholder="请输入账期" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账户名称" prop="accountName">
              <el-select v-model="form.accountName" placeholder="请选择账户名称" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.adur_account_name"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="市值" prop="marketValue">
              <el-input v-model="form.marketValue" placeholder="请输入市值" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账面余额" prop="bookBalance">
              <el-input v-model="form.bookBalance" placeholder="请输入账面余额" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="账面价值" prop="bookValue">
              <el-input v-model="form.bookValue" placeholder="请输入账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账面价值σ0%" prop="bookValueSigma0">
              <el-input v-model="form.bookValueSigma0" placeholder="请输入账面价值σ0%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="账面价值σ9%" prop="bookValueSigma9">
              <el-input v-model="form.bookValueSigma9" placeholder="请输入账面价值σ9%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账面价值σ17%" prop="bookValueSigma17">
              <el-input v-model="form.bookValueSigma17" placeholder="请输入账面价值σ17%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="账面价值σ77%" prop="bookValueSigma77">
              <el-input v-model="form.bookValueSigma77" placeholder="请输入账面价值σ77%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评估时点到期收益率" prop="evalMaturityYield">
              <el-input v-model="form.evalMaturityYield" placeholder="请输入评估时点到期收益率" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="评估时点资产现值" prop="evalPresentValue">
              <el-input v-model="form.evalPresentValue" placeholder="请输入评估时点资产现值" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资产修正久期" prop="assetModifiedDuration">
              <el-input v-model="form.assetModifiedDuration" placeholder="请输入资产修正久期" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="资产有效久期" prop="assetEffectiveDuration">
              <el-input v-model="form.assetEffectiveDuration" placeholder="请输入资产有效久期" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- DV10系列字段 - 分组显示 -->
        <el-divider content-position="left">DV10关键久期字段</el-divider>
        <el-row>
          <el-col :span="8">
            <el-form-item label="DV10_0" prop="dv100">
              <el-input v-model="form.dv100" placeholder="请输入DV10_0" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="DV10_0.5" prop="dv1005">
              <el-input v-model="form.dv1005" placeholder="请输入DV10_0.5" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="DV10_1" prop="dv101">
              <el-input v-model="form.dv101" placeholder="请输入DV10_1" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="DV10_2" prop="dv102">
              <el-input v-model="form.dv102" placeholder="请输入DV10_2" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="DV10_3" prop="dv103">
              <el-input v-model="form.dv103" placeholder="请输入DV10_3" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="DV10_4" prop="dv104">
              <el-input v-model="form.dv104" placeholder="请输入DV10_4" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="DV10_5" prop="dv105">
              <el-input v-model="form.dv105" placeholder="请输入DV10_5" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="DV10_6" prop="dv106">
              <el-input v-model="form.dv106" placeholder="请输入DV10_6" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="DV10_7" prop="dv107">
              <el-input v-model="form.dv107" placeholder="请输入DV10_7" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="DV10_8" prop="dv108">
              <el-input v-model="form.dv108" placeholder="请输入DV10_8" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="DV10_10" prop="dv1010">
              <el-input v-model="form.dv1010" placeholder="请输入DV10_10" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="DV10_12" prop="dv1012">
              <el-input v-model="form.dv1012" placeholder="请输入DV10_12" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="DV10_15" prop="dv1015">
              <el-input v-model="form.dv1015" placeholder="请输入DV10_15" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="DV10_20" prop="dv1020">
              <el-input v-model="form.dv1020" placeholder="请输入DV10_20" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="DV10_25" prop="dv1025">
              <el-input v-model="form.dv1025" placeholder="请输入DV10_25" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="DV10_30" prop="dv1030">
              <el-input v-model="form.dv1030" placeholder="请输入DV10_30" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="DV10_35" prop="dv1035">
              <el-input v-model="form.dv1035" placeholder="请输入DV10_35" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="DV10_40" prop="dv1040">
              <el-input v-model="form.dv1040" placeholder="请输入DV10_40" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="DV10_45" prop="dv1045">
              <el-input v-model="form.dv1045" placeholder="请输入DV10_45" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="DV10_50" prop="dv1050">
              <el-input v-model="form.dv1050" placeholder="请输入DV10_50" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import {
  listDurationAssetSummary,
  getDurationAssetSummary,
  delDurationAssetSummary,
  addDurationAssetSummary,
  updateDurationAssetSummary
} from "@/api/adur/durationAssetSummary";

export default {
  name: "DurationAssetSummary",
  dicts: ['adur_account_name'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 久期资产结果汇总表表格数据
      durationAssetSummaryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountPeriod: null,
        accountName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "账期格式必须为YYYYMM", trigger: "blur" }
        ],
        accountName: [
          { required: true, message: "账户名称不能为空", trigger: "change" }
        ]
      },

    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询久期资产结果汇总表列表 */
    getList() {
      this.loading = true;
      listDurationAssetSummary(this.queryParams).then(response => {
        this.durationAssetSummaryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountPeriod: null,
        accountName: null,
        marketValue: null,
        bookBalance: null,
        bookValue: null,
        bookValueSigma0: null,
        bookValueSigma9: null,
        bookValueSigma17: null,
        bookValueSigma77: null,
        evalMaturityYield: null,
        evalPresentValue: null,
        assetModifiedDuration: null,
        assetEffectiveDuration: null,
        dv100: null,
        dv1005: null,
        dv101: null,
        dv102: null,
        dv103: null,
        dv104: null,
        dv105: null,
        dv106: null,
        dv107: null,
        dv108: null,
        dv1010: null,
        dv1012: null,
        dv1015: null,
        dv1020: null,
        dv1025: null,
        dv1030: null,
        dv1035: null,
        dv1040: null,
        dv1045: null,
        dv1050: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加久期资产结果汇总表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getDurationAssetSummary(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改久期资产结果汇总表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateDurationAssetSummary(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDurationAssetSummary(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除久期资产结果汇总表编号为"' + ids + '"的数据项？').then(function() {
        return delDurationAssetSummary(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        'adur/duration/asset/summary/export',
        {
          ...this.queryParams
        },
        `ADUR久期资产结果汇总_${new Date().getTime()}.xlsx`
      );
    },

  }
};
</script>
