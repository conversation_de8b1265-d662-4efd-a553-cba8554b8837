<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountingPeriod">
        <el-input
          v-model="queryParams.accountingPeriod"
          placeholder="请输入账期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账户名称" prop="accountName">
        <el-select v-model="queryParams.accountName" placeholder="请选择账户名称" clearable>
          <el-option
            v-for="dict in dict.type.ast_account_name_mapping"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['pir:cost:revenue:match:pressure:detail:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['pir:cost:revenue:match:pressure:detail:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['pir:cost:revenue:match:pressure:detail:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['pir:cost:revenue:match:pressure:detail:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['pir:cost:revenue:match:pressure:detail:import']"
        >导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-tooltip placement="top" effect="dark">
          <div slot="content" style="max-width: 400px; line-height: 1.5;">
            <strong>压力测试规则：</strong><br/>
            1. 利差扩大77%：利差扩大只评估以公允价值计量的固定收益类投资资产<br/>
            2. 境内债券型基金按照公司报送99%置信区间下三年VaR情况计算损失<br/>
            3. 不可计算现金流的固定收益类保险资产管理产品、境外债券型基金、货币类保险资产管理产品等按照公司报送99%置信区间下境内债券型基金三年VaR值与境内债券型基金账面价值的比例计算损失<br/>
            4. 固定收益类投资资产风险分类关注类及以下的资产发生违约，产生相当于其对应资产账面价值60%的损失<br/>
            5. 境内外股票和基金根据公司报送99%置信区间下三年VaR值情况下跌<br/>
            6. 除境内外股票和基金外的权益类投资资产按账面价值的15%计提减值损失<br/>
            7. 对于按公允价值计价的上述资产，根据对应资产的账面价值乘以80%，得到压力情景下的账面价值
          </div>
          <el-input
            placeholder="备注说明"
            readonly
            size="mini"
            style="width: 120px; cursor: help;"
            value="备注说明"
          />
        </el-tooltip>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="costRevenueMatchPressureDetailList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountingPeriod" />
      <el-table-column label="账户名称" align="center" prop="accountName">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ast_account_name_mapping" :value="scope.row.accountName"/>
        </template>
      </el-table-column>
      <el-table-column label="账面价值1压力前" align="center" prop="bookValue1BeforeStress" />
      <el-table-column label="账面价值1压力后" align="center" prop="bookValue1AfterStress" />
      <el-table-column label="账面价值1变动值" align="center" prop="bookValue1Change" />
      <el-table-column label="账面价值2压力前" align="center" prop="bookValue2BeforeStress" />
      <el-table-column label="账面价值2压力后" align="center" prop="bookValue2AfterStress" />
      <el-table-column label="账面价值2变动值" align="center" prop="bookValue2Change" />
      <el-table-column label="账面价值3压力前" align="center" prop="bookValue3BeforeStress" />
      <el-table-column label="账面价值3压力后" align="center" prop="bookValue3AfterStress" />
      <el-table-column label="账面价值3变动值" align="center" prop="bookValue3Change" />
      <el-table-column label="账面价值4压力前" align="center" prop="bookValue4BeforeStress" />
      <el-table-column label="账面价值4压力后" align="center" prop="bookValue4AfterStress" />
      <el-table-column label="账面价值4变动值" align="center" prop="bookValue4Change" />
      <el-table-column label="账面价值5压力前" align="center" prop="bookValue5BeforeStress" />
      <el-table-column label="账面价值5压力后" align="center" prop="bookValue5AfterStress" />
      <el-table-column label="账面价值5变动值" align="center" prop="bookValue5Change" />
      <el-table-column label="账面价值6压力前" align="center" prop="bookValue6BeforeStress" />
      <el-table-column label="账面价值6压力后" align="center" prop="bookValue6AfterStress" />
      <el-table-column label="账面价值6变动值" align="center" prop="bookValue6Change" />
      <el-table-column label="账面价值7压力前" align="center" prop="bookValue7BeforeStress" />
      <el-table-column label="账面价值7压力后" align="center" prop="bookValue7AfterStress" />
      <el-table-column label="账面价值7变动值" align="center" prop="bookValue7Change" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['pir:cost:revenue:match:pressure:detail:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['pir:cost:revenue:match:pressure:detail:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改成本收益匹配压力一明细表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="账期" prop="accountingPeriod">
              <el-input v-model="form.accountingPeriod" placeholder="请输入账期" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账户名称" prop="accountName">
              <el-select v-model="form.accountName" placeholder="请选择账户名称" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.ast_account_name_mapping"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 账面价值1（利差久期资产） -->
        <el-divider content-position="left">账面价值1（利差久期资产）</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="压力前" prop="bookValue1BeforeStress">
              <el-input v-model="form.bookValue1BeforeStress" placeholder="请输入压力前账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="压力后" prop="bookValue1AfterStress">
              <el-input v-model="form.bookValue1AfterStress" placeholder="请输入压力后账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="变动值" prop="bookValue1Change">
              <el-input v-model="form.bookValue1Change" placeholder="请输入价值变动金额" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 账面价值2（债券型基金） -->
        <el-divider content-position="left">账面价值2（债券型基金）</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="压力前" prop="bookValue2BeforeStress">
              <el-input v-model="form.bookValue2BeforeStress" placeholder="请输入压力前账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="压力后" prop="bookValue2AfterStress">
              <el-input v-model="form.bookValue2AfterStress" placeholder="请输入压力后账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="变动值" prop="bookValue2Change">
              <el-input v-model="form.bookValue2Change" placeholder="请输入价值变动金额" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 账面价值3（固收资管产品） -->
        <el-divider content-position="left">账面价值3（固收资管产品）</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="压力前" prop="bookValue3BeforeStress">
              <el-input v-model="form.bookValue3BeforeStress" placeholder="请输入压力前账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="压力后" prop="bookValue3AfterStress">
              <el-input v-model="form.bookValue3AfterStress" placeholder="请输入压力后账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="变动值" prop="bookValue3Change">
              <el-input v-model="form.bookValue3Change" placeholder="请输入价值变动金额" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 账面价值4（五级分类资产） -->
        <el-divider content-position="left">账面价值4（五级分类资产）</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="压力前" prop="bookValue4BeforeStress">
              <el-input v-model="form.bookValue4BeforeStress" placeholder="请输入压力前账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="压力后" prop="bookValue4AfterStress">
              <el-input v-model="form.bookValue4AfterStress" placeholder="请输入压力后账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="变动值" prop="bookValue4Change">
              <el-input v-model="form.bookValue4Change" placeholder="请输入价值变动金额" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 账面价值5（股票基金） -->
        <el-divider content-position="left">账面价值5（股票基金）</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="压力前" prop="bookValue5BeforeStress">
              <el-input v-model="form.bookValue5BeforeStress" placeholder="请输入压力前账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="压力后" prop="bookValue5AfterStress">
              <el-input v-model="form.bookValue5AfterStress" placeholder="请输入压力后账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="变动值" prop="bookValue5Change">
              <el-input v-model="form.bookValue5Change" placeholder="请输入价值变动金额" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 账面价值6（其他权益类） -->
        <el-divider content-position="left">账面价值6（其他权益类）</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="压力前" prop="bookValue6BeforeStress">
              <el-input v-model="form.bookValue6BeforeStress" placeholder="请输入压力前账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="压力后" prop="bookValue6AfterStress">
              <el-input v-model="form.bookValue6AfterStress" placeholder="请输入压力后账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="变动值" prop="bookValue6Change">
              <el-input v-model="form.bookValue6Change" placeholder="请输入价值变动金额" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 账面价值7（投资性房地产） -->
        <el-divider content-position="left">账面价值7（投资性房地产）</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="压力前" prop="bookValue7BeforeStress">
              <el-input v-model="form.bookValue7BeforeStress" placeholder="请输入压力前账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="压力后" prop="bookValue7AfterStress">
              <el-input v-model="form.bookValue7AfterStress" placeholder="请输入压力后账面价值" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="变动值" prop="bookValue7Change">
              <el-input v-model="form.bookValue7Change" placeholder="请输入价值变动金额" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 成本收益匹配压力一明细表导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCostRevenueMatchPressureDetail, getCostRevenueMatchPressureDetail, delCostRevenueMatchPressureDetail, addCostRevenueMatchPressureDetail, updateCostRevenueMatchPressureDetail, exportCostRevenueMatchPressureDetail, importTemplate } from "@/api/pir/cost/revenue/match/pressure/detail";
import { getToken } from "@/utils/auth";

export default {
  name: "CostRevenueMatchPressureDetail",
  dicts: ['ast_account_name_mapping'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 成本收益匹配压力一明细表表格数据
      costRevenueMatchPressureDetailList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountingPeriod: null,
        accountName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountingPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" },
          { pattern: /^\d{6}$/, message: "账期格式不正确，应为YYYYMM格式", trigger: "blur" }
        ],
        accountName: [
          { required: true, message: "账户名称不能为空", trigger: "change" }
        ]
      },
      // 成本收益匹配压力一明细表导入参数
      upload: {
        // 是否显示弹出层（成本收益匹配压力一明细表导入）
        open: false,
        // 弹出层标题（成本收益匹配压力一明细表导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/pir/cost/revenue/match/pressure/detail/importData"
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询成本收益匹配压力一明细表列表 */
    getList() {
      this.loading = true;
      listCostRevenueMatchPressureDetail(this.queryParams).then(response => {
        this.costRevenueMatchPressureDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountingPeriod: null,
        accountName: null,
        bookValue1BeforeStress: null,
        bookValue1AfterStress: null,
        bookValue1Change: null,
        bookValue2BeforeStress: null,
        bookValue2AfterStress: null,
        bookValue2Change: null,
        bookValue3BeforeStress: null,
        bookValue3AfterStress: null,
        bookValue3Change: null,
        bookValue4BeforeStress: null,
        bookValue4AfterStress: null,
        bookValue4Change: null,
        bookValue5BeforeStress: null,
        bookValue5AfterStress: null,
        bookValue5Change: null,
        bookValue6BeforeStress: null,
        bookValue6AfterStress: null,
        bookValue6Change: null,
        bookValue7BeforeStress: null,
        bookValue7AfterStress: null,
        bookValue7Change: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加成本收益匹配压力一明细表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getCostRevenueMatchPressureDetail(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改成本收益匹配压力一明细表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCostRevenueMatchPressureDetail(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCostRevenueMatchPressureDetail(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除成本收益匹配压力一明细表编号为"' + ids + '"的数据项？').then(function() {
        return delCostRevenueMatchPressureDetail(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('pir/cost/revenue/match/pressure/detail/export', {
        ...this.queryParams
      }, `成本收益匹配压力一明细表_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "成本收益匹配压力一明细表导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('pir/cost/revenue/match/pressure/detail/importTemplate', {}, `成本收益匹配压力一明细表导入模板_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>
