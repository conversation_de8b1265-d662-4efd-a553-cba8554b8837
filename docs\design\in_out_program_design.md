# 源数据及报表管理 - 批量导入导出功能设计文档

## 1. 业务架构

### 1.1 业务模块关系图

*描述源数据及报表管理功能与各业务模块的关系*

```mermaid
flowchart TD
    A[源数据及报表管理] --> B[资产宽表模块AST]
    A --> C[资产配置状态模块ASM]
    A --> D[资产信用状态模块ACM]
    A --> E[久期分析模块ADUR]
    A --> F[成本核算模块COST]
    A --> G[现金流测试模块CFT]
    A --> H[最低资本模块MINC]
    A --> I[负债产品模块LIAB]
    A --> J[负责久期管理模块DUR]
    A --> K[投资收益率模块PIR]

    B --> L[批量导入]
    C --> L
    D --> L
    E --> L
    F --> L
    G --> L
    H --> L
    I --> L
    J --> L
    K --> L

    B --> M[批量导出]
    C --> M
    D --> M
    E --> M
    F --> M
    G --> M
    H --> M
    I --> M
    J --> M
    K --> M
```

### 1.2 模块列表

*描述各模块的基础信息（编号、名称、英文名、英文缩写）*

| 模块编号   | 模块名称     | 模块英文名                        | 英文缩写 | 备注                    |
| ------ | -------- | ---------------------------- | ---- | --------------------- |
| MD0001 | 源数据及报表管理 | import_export_management     | iem  | 统一的数据导入导出管理功能         |
| MD0002 | 资产宽表模块   | asset_wide_table             | ast  | 提供基础资产数据              |
| MD0003 | 资产配置状态模块 | asset_configuration_status   | asm  | 资产配置状态分析              |
| MD0004 | 资产信用状态模块 | asset_credit_status          | acm  | 资产信用状态分析              |
| MD0005 | 久期分析模块   | asset_duration_analysis      | adur | 资产久期分析                |
| MD0006 | 成本核算模块   | cost_accounting              | cost | 成本核算分析                |
| MD0007 | 现金流测试模块  | cash_flow_test               | cft  | 现金流测试分析               |
| MD0008 | 最低资本模块   | minimum_capital              | minc | 最低资本计算                |
| MD0009 | 负债产品模块   | liability_products           | liab | 负债产品管理                |
| MD0010 | 负责久期管理模块 | duration_management          | dur  | 负债久期管理                |
| MD0011 | 投资收益率模块  | portfolio_investment_returns | pir  | 投资收益率分析               |

### 1.3 数据模型

#### 1.3.1 源数据及报表管理模块

*描述源数据及报表管理模块下的表间关系及表属性信息*

##### 1.3.1.1 表间关系

*表间关系用mermaid图描述，主要用于梳理关系*

```mermaid
erDiagram
    t_data_import_export_log ||--o{ t_table_mapping_config : "references"
    t_table_mapping_config ||--o{ t_dict_mapping_config : "contains"
    t_jmreport_config ||--|| t_table_mapping_config : "maps_to"
    t_data_import_export_log {
        bigint id PK
        varchar operation_type
        varchar status
        varchar accounting_period
        varchar file_name
        datetime start_time
        datetime end_time
    }
    t_table_mapping_config {
        bigint id PK
        varchar table_name
        varchar table_code
        varchar module_code
        varchar entity_class
    }
    t_dict_mapping_config {
        bigint id PK
        varchar table_code FK
        varchar field_name
        varchar dict_type
    }
    t_jmreport_config {
        bigint id PK
        varchar report_name
        varchar report_code
        varchar excel_config_id
        varchar module_code
    }
```

##### 1.3.1.2 表名字典

*列出源数据及报表管理模块所有表信息*

| 表编号    | 表中文名       | 表英文名                        | 备注                    |
| ------ | ---------- | --------------------------- | --------------------- |
| TB0001 | 导入导出操作记录表  | t_data_import_export_log    | 记录所有导入导出操作的详细信息       |
| TB0002 | 表名映射配置表    | t_table_mapping_config      | 配置Sheet名称与数据表的映射关系    |
| TB0003 | 字典映射配置表    | t_dict_mapping_config       | 配置字段与字典类型的映射关系        |
| TB0004 | 积木报表配置表    | t_jmreport_config           | 配置积木报表的导出信息           |

##### 1.3.1.3 表集

*描述详细的表属性信息，id（默认为主键）、create_time、update_time、create_by、update_by、is_del等字段不需要描述，后续生成DDL时会自动补充*
*唯一索引：这里唯一索引是指业务字段单字段或多字段组合的唯一性，如多字段组合的情况，在所有字段唯一索引列设置为"是"*
*说明：描述字段的作用，枚举类型字段格式为描述,value1:label1[,...,valueN:labelN]，这里一定要注意格式，在后续开发步骤中会通过这里的字段枚举描述生成字段数据*

**（1）TB0001 - 导入导出操作记录表**

*记录所有导入导出操作的详细信息，包括操作状态、处理结果、错误信息等*

| 字段名                    | 数据类型      | 长度      | 允许空 | 唯一索引 | 默认值 | 说明                                    |
| ---------------------- | --------- | ------- | --- | ---- | --- | ------------------------------------- |
| **operation_type**     | varchar   | 10      | 否   | 否    | 无   | 操作类型,IMPORT:导入,EXPORT:导出             |
| **status**             | varchar   | 20      | 否   | 否    | 无   | 状态,SUCCESS:成功,FAILED:失败,PROCESSING:处理中 |
| accounting_period      | varchar   | 6       | 是   | 否    | 无   | 账期，格式YYYYMM                          |
| file_name              | varchar   | 255     | 是   | 否    | 无   | 文件名称                                  |
| file_path              | varchar   | 500     | 是   | 否    | 无   | 文件存储路径                                |
| total_records          | int       | 11      | 是   | 否    | 0   | 总记录数                                  |
| success_records        | int       | 11      | 是   | 否    | 0   | 成功记录数                                 |
| failed_records         | int       | 11      | 是   | 否    | 0   | 失败记录数                                 |
| start_time             | datetime  | 无       | 是   | 否    | 无   | 开始时间                                  |
| end_time               | datetime  | 无       | 是   | 否    | 无   | 结束时间                                  |
| error_message          | mediumtext| 无       | 是   | 否    | 无   | 错误信息                                  |
| remark                 | varchar   | 1000    | 是   | 否    | 无   | 备注                                    |

**（2）TB0002 - 表名映射配置表**

*配置Sheet名称与数据表的映射关系，用于导入时识别目标表*

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                    |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------------------- |
| **table_name**         | varchar | 100   | 否   | 是    | 无   | 表名（中文）                              |
| **table_code**         | varchar | 100   | 否   | 是    | 无   | 表编码（英文）                             |
| **module_code**        | varchar | 20    | 否   | 否    | 无   | 模块编码                                |
| menu_path              | varchar | 200   | 是   | 否    | 无   | 菜单路径                                |
| entity_class           | varchar | 200   | 是   | 否    | 无   | 实体类名                                |
| import_enabled         | tinyint | 1     | 是   | 否    | 1   | 是否支持导入,0:否,1:是                      |
| export_enabled         | tinyint | 1     | 是   | 否    | 1   | 是否支持导出,0:否,1:是                      |
| sort_order             | int     | 11    | 是   | 否    | 0   | 排序                                  |
| remark                 | varchar | 500   | 是   | 否    | 无   | 备注                                  |

**（3）TB0003 - 字典映射配置表**

*配置字段与字典类型的映射关系，用于导入时进行字典转换*

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                    |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------------------- |
| **table_code**         | varchar | 100   | 否   | 是    | 无   | 表编码                                 |
| **field_name**         | varchar | 100   | 否   | 是    | 无   | 字段名                                 |
| **dict_type**          | varchar | 100   | 否   | 否    | 无   | 字典类型                                |
| is_required            | tinyint | 1     | 是   | 否    | 0   | 是否必填,0:否,1:是                        |
| default_value          | varchar | 100   | 是   | 否    | 无   | 默认值                                 |
| remark                 | varchar | 500   | 是   | 否    | 无   | 备注                                  |

**（4）TB0004 - 积木报表配置表**

*配置积木报表的导出信息，用于批量导出时调用积木报表API*

| 字段名                    | 数据类型    | 长度    | 允许空 | 唯一索引 | 默认值 | 说明                                    |
| ---------------------- | ------- | ----- | --- | ---- | --- | ------------------------------------- |
| **report_name**        | varchar | 100   | 否   | 否    | 无   | 报表名称                                |
| **report_code**        | varchar | 100   | 否   | 是    | 无   | 报表编码                                |
| **excel_config_id**    | varchar | 100   | 否   | 是    | 无   | 积木报表配置ID                           |
| **module_code**        | varchar | 20    | 否   | 否    | 无   | 模块编码                                |
| export_enabled         | tinyint | 1     | 是   | 否    | 1   | 是否支持导出,0:否,1:是                      |
| sort_order             | int     | 11    | 是   | 否    | 0   | 排序                                  |
| remark                 | varchar | 500   | 是   | 否    | 无   | 备注                                  |

### 1.4 用例列表

*列出所有用例场景，用例可理解为用户为完成某个作业目标而执行的一系列操作的集合*

| 用例编号   | 用例名称       | 用例描述                     | 模块编号   |
| ------ | ---------- | ------------------------ | ------ |
| UC0001 | 批量数据导入     | 用户上传Excel文件进行多表数据批量导入   | MD0001 |
| UC0002 | 批量报表导出     | 用户选择账期进行多报表批量导出        | MD0001 |
| UC0003 | 导入导出状态查询   | 用户查询导入导出操作的状态和结果       | MD0001 |
| UC0004 | 表映射配置管理    | 管理员配置表名与Sheet的映射关系      | MD0001 |
| UC0005 | 字典映射配置管理   | 管理员配置字段与字典的映射关系        | MD0001 |
| UC0006 | 积木报表配置管理   | 管理员配置积木报表的导出信息         | MD0001 |

## 2. 功能需求

### 2.1 页面布局设计

#### 2.1.1 搜索条件区域
- **类型**：下拉选择框，选项包括"导入"、"导出"
- **状态**：下拉选择框，选项包括"成功"、"失败"、"处理中"
- **备注**：文本输入框，支持模糊查询
- **操作时间**：日期范围选择器

#### 2.1.2 操作按钮区域
- **导入**：触发批量数据导入功能
- **导出**：触发批量报表导出功能
- **刷新**：刷新当前页面数据
- **清空**：清空搜索条件

#### 2.1.3 数据展示区域
表格字段包括：
- **ID**：操作记录唯一标识
- **类型**：导入/导出
- **状态**：成功/失败/处理中
- **操作时间**：操作发起时间
- **完成时间**：操作完成时间
- **处理文件**：导入的文件名或导出的文件名
- **账期**：相关的账期信息
- **备注**：详细的操作信息或错误信息
- **操作**：查看详情、下载文件等操作按钮

### 2.2 导入功能需求

#### 2.2.1 文件格式要求
- **文件类型**：Excel文件(.xlsx, .xls)
- **Sheet命名规则**：`模块英文缩写_表名`
  - 示例：`ast_资产整体明细表`、`acm_信用评级表`、`asm_偿付能力状况表`
- **支持多Sheet**：单个Excel文件可包含多个Sheet，每个Sheet对应一个数据表

#### 2.2.2 导入流程设计
1. **文件上传**：用户选择Excel文件进行上传
2. **Sheet解析**：系统解析文件中的所有Sheet
3. **表名映射**：根据Sheet名称中的表名，通过配置表确定目标菜单和数据表
4. **数据验证**：对每个Sheet的数据进行格式和业务规则验证
5. **字典转换**：将Excel中的中文标签转换为系统字典编码
6. **数据导入**：将验证通过的数据批量插入到对应的数据表
7. **结果反馈**：记录导入结果，包括成功数量和失败详情

#### 2.2.3 错误处理策略
- **失败即停止**：任何Sheet导入失败时，停止整个导入过程
- **详细错误信息**：记录具体的失败原因，包括：
  - Sheet名称
  - 行号和列号
  - 具体错误原因
  - 数据值信息

### 2.3 导出功能需求

#### 2.3.1 导出流程设计
1. **账期选择**：用户输入或选择要导出的账期
2. **报表查询**：系统根据账期查询所有相关报表数据
3. **数据整合**：将多个报表数据整合到一个Excel文件的不同Sheet中
4. **文件生成**：生成Excel文件并提供下载
5. **记录日志**：记录导出操作的详细信息

#### 2.3.2 导出文件格式
- **文件命名**：`报表数据_账期_导出时间.xlsx`
- **Sheet命名**：固定格式，如`表4-4 现金流测试表_万能保险账户`
- **积木报表URL**：每个Sheet对应一个积木报表导出URL
- **数据格式**：
  - 字典编码转换为中文标签
  - 数值字段保留适当的小数位数
  - 日期字段使用中文格式

### 2.4 积木报表配置

#### 2.4.1 报表配置示例

*列出各模块主要报表的积木报表配置信息*

| 报表名称                | Sheet表名                    | 积木报表URL                                      | 模块编码 | 备注           |
| ------------------- | -------------------------- | ---------------------------------------------- | ---- | ------------ |
| 资产整体明细表             | ast_资产整体明细表                | /jmreport/exportAllExcelStream                 | ast  | 基础资产数据       |
| 资金运用规模表             | asm_资金运用规模表                | /jmreport/exportAllExcelStream                 | asm  | 资金运用规模统计     |
| 固定收益类投资资产信用评级表      | acm_固定收益类投资资产信用评级表         | /jmreport/exportAllExcelStream                 | acm  | 信用评级统计       |
| 表4-4 现金流测试表_万能保险账户 | 表4-4 现金流测试表_万能保险账户       | /jmreport/exportAllExcelStream                 | cft  | 现金流测试报表      |
| 折现曲线表               | adur_折现曲线表                 | /jmreport/exportAllExcelStream                 | adur | 久期分析报表       |
| 成本核算汇总表             | cost_成本核算汇总表               | /jmreport/exportAllExcelStream                 | cost | 成本核算报表       |

#### 2.4.2 导出参数配置

*积木报表导出时的标准参数配置*

```json
{
  "excelConfigId": "1087231279847813120",
  "queryParam": {
    "token": "dd486eb7-9ac2-40a1-8510-3a8e62523cfd",
    "tenantId": "1",
    "pageNo": "1",
    "pageSize": 10,
    "accountingPeriod": "202401"
  },
  "currentPageNo": "1",
  "currentPageSize": 10,
  "customTableTitleSorts": []
}
```

## 3. 技术实现方案

### 3.1 后端技术架构

#### 3.1.1 核心组件设计
- **ImportExportController**：控制器层，处理导入导出请求
- **ImportExportService**：业务逻辑层，实现导入导出核心功能
- **TableMappingConfigService**：表映射配置服务
- **DictMappingConfigService**：字典映射配置服务
- **JmreportConfigService**：积木报表配置服务
- **ExcelProcessor**：Excel文件处理器
- **DictConverter**：字典转换器

#### 3.1.2 导入处理流程
1. **文件解析**：使用EasyExcel解析上传的Excel文件
2. **Sheet识别**：根据Sheet名称识别对应的数据表
3. **配置查询**：查询表映射配置和字典映射配置
4. **数据转换**：将Excel数据转换为实体对象
5. **数据验证**：执行业务规则验证
6. **批量插入**：使用MyBatis-Plus批量插入数据
7. **日志记录**：记录操作日志和错误信息

#### 3.1.3 导出处理流程
1. **配置查询**：查询积木报表配置信息
2. **数据查询**：调用积木报表API获取数据
3. **数据转换**：将字典编码转换为中文标签
4. **文件生成**：使用EasyExcel生成多Sheet的Excel文件
5. **文件存储**：将生成的文件存储到指定位置
6. **日志记录**：记录导出操作日志

### 3.2 前端技术架构

#### 3.2.1 页面组件设计
- **ImportExportIndex.vue**：主页面组件
- **ImportDialog.vue**：导入对话框组件
- **ExportDialog.vue**：导出对话框组件
- **DetailDialog.vue**：详情查看对话框组件

#### 3.2.2 状态管理
- 使用Vuex管理导入导出操作状态
- 实现操作进度的实时更新
- 支持操作结果的状态展示

### 3.3 文件存储方案

#### 3.3.1 存储目录结构
```
/data/alm/import_export/
├── import/
│   ├── 202401/
│   │   ├── success/
│   │   └── failed/
│   └── 202402/
└── export/
    ├── 202401/
    └── 202402/
```

#### 3.3.2 文件命名规范
- **导入文件**：`import_原文件名_时间戳.xlsx`
- **导出文件**：`export_账期_时间戳.xlsx`
- **错误日志**：`error_操作ID_时间戳.log`

## 4. 配置数据示例

### 4.1 表名映射配置数据示例

*列出各模块主要表的映射配置信息*

| 表名（中文）        | 表编码（英文）                        | 模块编码 | 实体类名                        | 导入支持 | 导出支持 |
| ------------- | ------------------------------ | ---- | --------------------------- | ---- | ---- |
| 资产整体明细表       | t_ast_asset_detail_overall     | ast  | AssetDetailOverallEntity    | 是    | 是    |
| 资产基础配置表       | t_ast_asset_basic_config       | ast  | AssetBasicConfigEntity      | 是    | 是    |
| 账户名称映射表       | t_ast_account_name_map         | ast  | AccountNameMapEntity        | 是    | 是    |
| VaR值分析表       | t_ast_var_analysis             | ast  | VarAnalysisEntity           | 是    | 是    |
| 资金运用规模表       | t_asm_fund_utilization_scale   | asm  | FundUtilizationScaleEntity  | 是    | 是    |
| 固定收益类投资资产信用评级表 | t_acm_fixed_income_credit_rating | acm  | FixedIncomeCreditRatingEntity | 是    | 是    |
| 折现曲线表         | t_adur_discount_curve          | adur | DiscountCurveEntity         | 是    | 是    |
| 成本核算汇总表       | t_cost_account_summary         | cost | AccountSummaryEntity        | 是    | 是    |

### 4.2 字典映射配置数据示例

*列出主要字段的字典映射配置*

| 表编码                            | 字段名                    | 字典类型                    | 是否必填 | 默认值 |
| ------------------------------ | ---------------------- | ----------------------- | ---- | --- |
| t_ast_asset_detail_overall     | account_name           | ast_account_name_mapping | 是    | 无   |
| t_ast_asset_detail_overall     | asset_sub_sub_category | asset_sub_sub_category  | 是    | 无   |
| t_ast_asset_detail_overall     | bond_type              | bond_type               | 否    | 无   |
| t_ast_asset_detail_overall     | accounting_classification | accounting_classification | 否    | 无   |
| t_ast_asset_basic_config       | calculable_cashflow_flag | yes_no_flag             | 否    | 0   |
| t_acm_fixed_income_credit_rating | credit_rating_category | credit_rating_category  | 是    | 无   |
| t_asm_fund_utilization_scale   | data_type              | data_type               | 是    | 无   |

### 4.3 积木报表配置数据示例

*列出各模块主要报表的积木报表配置*

| 报表名称          | 报表编码                        | 积木报表配置ID        | 模块编码 | 导出支持 |
| ------------- | --------------------------- | ---------------- | ---- | ---- |
| 资产整体明细表       | ast_asset_detail_overall    | 1087231279847813120 | ast  | 是    |
| 资金运用规模表       | asm_fund_utilization_scale  | 1087231279847813121 | asm  | 是    |
| 固定收益类投资资产信用评级表 | acm_fixed_income_credit_rating | 1087231279847813122 | acm  | 是    |
| 表4-4 现金流测试表   | cft_cash_flow_test_table    | 1087231279847813123 | cft  | 是    |
| 折现曲线表         | adur_discount_curve         | 1087231279847813124 | adur | 是    |
| 成本核算汇总表       | cost_account_summary        | 1087231279847813125 | cost | 是    |

## 5. 异常处理与性能优化

### 5.1 异常处理机制

#### 5.1.1 导入异常处理
- **文件格式异常**：文件格式不支持、文件损坏，返回明确错误信息
- **Sheet名称异常**：Sheet名称不符合规范，提供正确命名示例
- **数据验证异常**：必填字段为空、数据格式错误，记录详细错误位置
- **业务规则异常**：数据重复、外键约束违反，提供修正建议

#### 5.1.2 导出异常处理
- **数据查询异常**：账期数据不存在、查询超时，支持重试机制
- **文件生成异常**：磁盘空间不足、文件写入失败，清理临时文件

### 5.2 性能优化方案

#### 5.2.1 导入性能优化
- **批量处理**：每批处理1000条记录，避免内存溢出
- **并行处理**：支持多Sheet并行导入，提高处理效率
- **流式读取**：使用EasyExcel流式读取，避免大文件内存溢出
- **缓存策略**：缓存字典数据和配置信息，减少数据库查询

#### 5.2.2 导出性能优化
- **异步处理**：导出任务异步执行，避免前端长时间等待
- **分页查询**：大数据量时使用分页查询
- **压缩存储**：对大文件进行压缩存储，节省存储空间

### 5.3 安全控制
- **权限控制**：基于角色的功能权限和数据权限控制
- **文件安全**：严格检查文件类型、限制文件大小、病毒扫描
- **数据安全**：SQL注入防护、数据脱敏、访问控制

## 6. 部署与运维

### 6.1 环境要求
- **JDK版本**：JDK 8+
- **数据库**：MySQL 5.7+
- **存储空间**：至少100GB可用空间
- **内存要求**：建议8GB+内存

### 6.2 配置参数示例
```yaml
# 导入导出配置
import-export:
  file-storage-path: /data/alm/import_export
  max-file-size: 100
  batch-size: 1000
  async-pool-size: 10
  file-retention-days: 30
```

### 6.3 监控指标
- **操作成功率**：导入导出操作的成功率统计
- **处理时间**：操作处理时间的监控和告警
- **文件大小**：处理文件大小的统计分析
- **错误频率**：各类错误的发生频率统计

---

**文档版本**：v1.0
**创建日期**：2025-01-07
**最后更新**：2025-01-07
**文档状态**：待评审