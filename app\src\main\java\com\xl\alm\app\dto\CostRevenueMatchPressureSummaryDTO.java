package com.xl.alm.app.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 成本收益匹配压力一汇总表数据传输对象
 *
 * <AUTHOR> Assistant
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CostRevenueMatchPressureSummaryDTO extends BaseDTO {
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键
     */
    private Long id;
    
    /**
     * 账期，格式YYYYMM（如202406）
     */
    @Excel(name = "账期")
    @ExcelProperty("账期")
    @NotBlank(message = "账期不能为空")
    @Pattern(regexp = "^\\d{6}$", message = "账期格式不正确，应为YYYYMM格式")
    private String accountingPeriod;
    
    /**
     * 账户名称，引用字典ast_account_name_mapping
     */
    @Excel(name = "账户名称", dictType = "ast_account_name_mapping")
    @ExcelProperty("账户名称")
    @NotBlank(message = "账户名称不能为空")
    @Size(max = 50, message = "账户名称长度不能超过50个字符")
    private String accountName;
    
    /**
     * 项目名称，建议新建字典pir_asset_category：01:固定收益类投资资产,02:权益类投资资产,03:投资性房地产
     */
    @Excel(name = "项目名称", dictType = "pir_asset_category")
    @ExcelProperty("项目名称")
    @NotBlank(message = "项目名称不能为空")
    @Size(max = 50, message = "项目名称长度不能超过50个字符")
    private String itemName;
    
    /**
     * 变动值，压力测试后的价值变动金额，单位：元
     */
    @Excel(name = "变动值", cellType = Excel.ColumnType.NUMERIC, scale = 2)
    @ExcelProperty("变动值")
    private BigDecimal changeValue;
}
